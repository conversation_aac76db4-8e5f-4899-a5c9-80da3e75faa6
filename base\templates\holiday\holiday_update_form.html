{% load i18n %}
{% if messages %}
    <span hx-get="{% url 'holiday-filter' %}?{{pd}}" hx-target="#holidays" hx-trigger="load" hx-on-htmx-after-request="setTimeout(() => {
            $('.oh-modal__close').click();
        }, 1000);">
    </span>
{% endif %}
<div class="oh-modal__dialog-header pb-0">
    <span class="oh-modal__dialog-title" id="editDialogDialog">{% trans "Update" %} {{ form.verbose_name }}</span>
    <button class="oh-modal__close" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body">
    <form hx-post="{% url 'holiday-update' id %}?{{pd}}" hx-target="#objectUpdateModalTarget"
        class="oh-profile-section pt-0">
        <label class="oh-label d-block" for="{{ form.name.id_for_label }}">
            {{ form.name.label }}
        </label>
        {{ form.name }} {{ form.name.errors }}
        <div class="row">
            <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                <div class="oh-input__group">
                    <label class="oh-input__label" for="{{form.start_date.id_for_label}}">
                        {{form.start_date.label}}
                    </label>
                    {{ form.start_date }} {{ form.start_date.errors }}
                </div>
            </div>
            <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                <div class="oh-input__group">
                    <label class="oh-input__label" for="{{form.end_date.id_for_label}}">
                        {{form.end_date.label}}
                    </label>
                    {{ form.end_date }} {{ form.end_date.errors }}
                </div>
            </div>
            <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                <div class="oh-input__group">
                    <label class="oh-input__label" for="{{form.company_id.id_for_label}}">
                        {{form.company_id.label}}
                    </label>
                    {{ form.company_id }} {{ form.company_id.errors }}
                </div>
            </div>
            <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                <label class="oh-label d-block" for="{{ form.recurring.id_for_label }}">
                    {{ form.recurring.label }}
                </label>
                <div class="oh-switch">
                    {{ form.recurring }} {{ form.recurring.errors }}
                </div>
            </div>
        </div>
        <div class="oh-modal__dialog-footer p-0">
            <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
                {% trans "Save" %}
            </button>
        </div>
    </form>
</div>
