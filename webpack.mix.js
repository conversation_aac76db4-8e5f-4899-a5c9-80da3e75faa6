const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

// Set the public path for compiled assets
mix.setPublicPath('static/build');

// Compile SCSS to CSS
mix.sass('static/src/scss/main.scss', 'css/style.min.css')
   .options({
       processCssUrls: false,
       postCss: [require('autoprefixer')]
   });

// Copy and version assets
mix.copy('static/src/scss/main.css', 'static/build/css/main.css', false);

// Enable source maps in non-production
if (!mix.inProduction()) {
    mix.sourceMaps();
}

// Enable versioning in production
if (mix.inProduction()) {
    mix.version();
}

// Set options
mix.options({
    processCssUrls: false,
    clearConsole: false
});