{% load static %} {% load i18n %} {% load horillafilters %}
{% if records %}
    <div class="oh-sticky-table__table mt-3">
        <div class="oh-sticky-table__thead">
            <div class="oh-sticky-table__tr">
                {% if "leave"|app_installed %}
                <div class="oh-sticky-table__th">{% trans "Leave Type" %}</div>
                <div class="oh-sticky-table__th" style="width: 110px">{% trans "Minus Days" %}</div>
                <div class="oh-sticky-table__th">{% trans "Deducted From " %}<span
                        title="{% trans 'Carry Forward Days' %}">{% trans "CFD" %}</span></div>
                {% endif %}
                <div class="oh-sticky-table__th">{% trans "Penalty amount" %}</div>
                <div class="oh-sticky-table__th">{% trans "Created Date" %}</div>
            </div>
        </div>
        <div class="oh-sticky-table__tbody">
            {% for acc in records %}
            <div class="oh-sticky-table__tr">
                {% if "leave"|app_installed %}
                <div class="oh-sticky-table__td">{{ acc.leave_type_id }}</div>
                <div class="oh-sticky-table__td">{{ acc.minus_leaves }}</div>
                <div class="oh-sticky-table__td">{{acc.deduct_from_carry_forward|yes_no}}</div>
                {% endif %}
                <div class="oh-sticky-table__td">
                    {{ acc.penalty_amount|currency_symbol_position }}
                </div>
                <div class="oh-sticky-table__td">{{ acc.created_at }}</div>
            </div>
            {% endfor %}
        </div>
    </div>
{% else %}
    <div class="oh-sticky-table__table mt-3">
        <div class="oh-404__subtitle">
            <img style="width: 190px; height: 190px" src="{% static 'images/ui/no_penalty.png' %}"
                class="oh-404__image mb-4" alt="Page not found. 404." />
            <h5 class="oh-404__subtitle">
                {% trans "No penalties found" %}
            </h5>
        </div>
    </div>
{% endif %}
