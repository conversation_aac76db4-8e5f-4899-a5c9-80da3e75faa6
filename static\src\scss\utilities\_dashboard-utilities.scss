// -----------------------------------------------------------------------------
// Dashboard Utility Classes
// -----------------------------------------------------------------------------

// Spacing utilities
.p-xs { padding: $dashboard-spacing-xs !important; }
.p-sm { padding: $dashboard-spacing-sm !important; }
.p-md { padding: $dashboard-spacing-md !important; }
.p-lg { padding: $dashboard-spacing-lg !important; }
.p-xl { padding: $dashboard-spacing-xl !important; }
.p-xxl { padding: $dashboard-spacing-xxl !important; }

.m-xs { margin: $dashboard-spacing-xs !important; }
.m-sm { margin: $dashboard-spacing-sm !important; }
.m-md { margin: $dashboard-spacing-md !important; }
.m-lg { margin: $dashboard-spacing-lg !important; }
.m-xl { margin: $dashboard-spacing-xl !important; }
.m-xxl { margin: $dashboard-spacing-xxl !important; }

// Shadow utilities
.shadow-sm { box-shadow: $dashboard-shadow-sm !important; }
.shadow-md { box-shadow: $dashboard-shadow-md !important; }
.shadow-lg { box-shadow: $dashboard-shadow-lg !important; }

// Border radius utilities
.rounded-sm { border-radius: $dashboard-radius-sm !important; }
.rounded-md { border-radius: $dashboard-radius-md !important; }
.rounded-lg { border-radius: $dashboard-radius-lg !important; }
.rounded-xl { border-radius: $dashboard-radius-xl !important; }

// Background utilities
.bg-primary { background-color: $dashboard-primary !important; }
.bg-secondary { background-color: $dashboard-secondary !important; }
.bg-success { background-color: $dashboard-success !important; }
.bg-warning { background-color: $dashboard-warning !important; }
.bg-danger { background-color: $dashboard-danger !important; }
.bg-light { background-color: $dashboard-bg-secondary !important; }
.bg-white { background-color: $dashboard-bg-tertiary !important; }

// Text color utilities
.text-primary { color: $dashboard-primary !important; }
.text-secondary { color: $dashboard-text-secondary !important; }
.text-muted { color: $dashboard-text-muted !important; }
.text-success { color: $dashboard-success !important; }
.text-warning { color: $dashboard-warning !important; }
.text-danger { color: $dashboard-danger !important; }

// Border utilities
.border-light { border: 1px solid $dashboard-border-light !important; }
.border-medium { border: 1px solid $dashboard-border-medium !important; }
.border-dark { border: 1px solid $dashboard-border-dark !important; }

// Flex utilities
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.gap-sm { gap: $dashboard-spacing-sm !important; }
.gap-md { gap: $dashboard-spacing-md !important; }
.gap-lg { gap: $dashboard-spacing-lg !important; }

// Animation utilities
.transition-fast { transition: all $dashboard-transition-fast !important; }
.transition-normal { transition: all $dashboard-transition-normal !important; }
.transition-slow { transition: all $dashboard-transition-slow !important; }

// Hover effects
.hover-lift {
  transition: transform $dashboard-transition-fast;
  
  &:hover {
    transform: translateY(-2px);
  }
}

.hover-scale {
  transition: transform $dashboard-transition-fast;
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-glow {
  transition: box-shadow $dashboard-transition-fast;
  
  &:hover {
    box-shadow: 0 0 20px rgba($dashboard-primary, 0.3);
  }
}

// Dashboard specific utilities
.dashboard-card-modern {
  @extend .oh-card-dashboard;
  
  background: linear-gradient(135deg, $dashboard-bg-tertiary, darken($dashboard-bg-tertiary, 1%));
  box-shadow: $dashboard-shadow-md;
  border: 1px solid $dashboard-border-light;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: $dashboard-shadow-lg;
  }
}

.dashboard-widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $dashboard-spacing-lg;
  background: linear-gradient(135deg, $dashboard-bg-secondary, $dashboard-bg-tertiary);
  border-bottom: 1px solid $dashboard-border-light;
  font-weight: 600;
}

.dashboard-stat-number {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
  background: linear-gradient(135deg, $dashboard-primary, $dashboard-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

// Responsive grid systems
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $dashboard-spacing-lg;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: $dashboard-spacing-md;
  }
}

.grid-2-cols {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $dashboard-spacing-lg;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.grid-3-cols {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: $dashboard-spacing-lg;
  
  @media (max-width: 968px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.grid-4-cols {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $dashboard-spacing-lg;
  
  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (max-width: 968px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// Loading states
.loading-skeleton {
  background: linear-gradient(90deg, $dashboard-border-light 25%, lighten($dashboard-border-light, 5%) 50%, $dashboard-border-light 75%);
  background-size: 200% 100%;
  animation: loading-skeleton 2s infinite;
}

@keyframes loading-skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Interactive elements
.clickable {
  cursor: pointer;
  transition: all $dashboard-transition-fast;
  
  &:hover {
    opacity: 0.8;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

// Status indicators
.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  
  &.status-online { background-color: $dashboard-success; }
  &.status-away { background-color: $dashboard-warning; }
  &.status-busy { background-color: $dashboard-danger; }
  &.status-offline { background-color: $dashboard-text-muted; }
}

// Modern gradients
.gradient-primary {
  background: linear-gradient(135deg, $dashboard-primary, $dashboard-secondary);
  color: white;
}

.gradient-success {
  background: linear-gradient(135deg, $dashboard-success, darken($dashboard-success, 15%));
  color: white;
}

.gradient-warning {
  background: linear-gradient(135deg, $dashboard-warning, darken($dashboard-warning, 15%));
  color: white;
}

.gradient-danger {
  background: linear-gradient(135deg, $dashboard-danger, darken($dashboard-danger, 15%));
  color: white;
}