{% load i18n %} {% load basefilters %}{% load widget_tweaks %}
{{ form.non_field_errors }}

<div class="row">
    {% for field in form %}
        {% if field.label != "Employee profile" and field.label != 'Country' and field.label != 'State' and field.label != 'Address' and field.label != 'Badge id' %}
            <div class="col-lg-6">
                <div class="oh-input__group">
                    <label class="oh-label {% if field.field.required %}required-star{% endif %}" for="id_{{ field.name }}"
                        title="{{ field.help_text|safe }}">
                        {% trans field.label %}
                    </label>

                    {% if field.field.widget.input_type == 'checkbox' %}
                        <div class="oh-switch" style="width: 30px;">{{ field|add_class:'oh-switch__checkbox' }}</div>
                    {% else %}
                        {{ field|add_class:'form-control' }}
                    {% endif %}

                    {{ field.errors }}
                </div>
            </div>
        {% elif field.label == 'Address' %}
            <div class="col-lg-12">
                <div class="oh-input__group">
                    <label class="oh-label {% if field.field.required %}required-star{% endif %}" for="id_{{ field.name }}"
                        title="{{ field.help_text|safe }}">
                        {% trans field.label %}
                    </label>
                    {{ field }}
                    {{ field.errors }}
                </div>
            </div>
        {% elif field.label == 'Country' %}
            <div class="row">
                <div class="col-lg-6">
                    <label class="oh-label" for="country">{% trans "Country" %}</label>
                    <select name="country" class="w-100 oh-select-2" id="id_country"
                        data-selected="{{ form.country.value|default:'' }}">
                    </select>
                    {{form.country.errors}}
                    <span class="dropdown-wrapper" aria-hidden="true"></span>
                    </span>
                </div>
                <div class="col-lg-6">
                    <label class="oh-label d-block" for="state">{% trans "State" %}</label>
                    <select name="state" class="w-100 oh-select-2" id="id_state"
                        data-selected="{{ form.state.value|default:'' }}">
                    </select>
                    {{form.state.errors}}
                </div>
            </div>
        {% endif %}
    {% endfor %}
</div>
