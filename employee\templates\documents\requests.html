{% load i18n %} {% load horillafilters %} {% load basefilters %} {% load static %}

{% include 'filter_tags.html' %}
<div class="oh-modal" id="viewFileModal" role="dialog" aria-labelledby="viewFileModal" aria-hidden="true">
	<div class="oh-modal__dialog custom-dialog">
		<div class="oh-modal__dialog-header">
			<span class="oh-modal__dialog-title" id="viewFileModalLabel">{% trans "View File" %}</span>
			<button class="oh-modal__close" aria-label="Close">
				<ion-icon name="close-outline" role="img" class="md hydrated" aria-label="close outline"></ion-icon>
			</button>
		</div>
		<div class="oh-modal__dialog-body" id="viewFile"></div>
	</div>
</div>

<div class="oh-modal" id="rejectFileModal" role="dialog" aria-labelledby="rejectFileModal" aria-hidden="true">
	<div class="oh-modal__dialog">
		<div class="oh-modal__dialog-header">
			<span class="oh-modal__dialog-title" id="rejectFileModalLabel">{% trans "Reject" %}</span>
			<button class="oh-modal__close--custom" aria-label="Close"
				onclick="event.stopPropagation();event.preventDefault();$('#rejectFileModal').toggleClass('oh-modal--show');">
				<ion-icon name="close-outline" role="img" class="md hydrated" aria-label="close outline"></ion-icon>
			</button>
		</div>
		<div class="oh-modal__dialog-body" id="rejectFileForm"></div>
	</div>
</div>

{% if documents %}
	<div class="oh-card">
		{% for document_list in documents reversed %}
			<div class="oh-accordion-meta">
				<div class="oh-accordion-meta__item">
					<div class="oh-accordion-meta__header">
						<span class="oh-accordion-meta__title pt-3 pb-3">
							<div class="oh-tabs__input-badge-container">
								<input type="checkbox" name="select_all" onclick="selectAllDocuments(event)"
									class="oh-input payslip-checkbox oh-input__checkbox select_all me-3">
									{{document_list.grouper}}
								<div class="oh-checkpoint-badge oh-checkpoint-badge--secondary" style="margin-left: 20px;"
									title="{% trans 'Uploaded / Requested' %}">
									{{document_list.list.0.upload_documents_count}} / {{document_list.list|length}}
								</div>
							</div>
						</span>

						<div class="oh-accordion-meta__actions" onclick="event.stopPropagation()">
							<div class="oh-dropdown" x-data="{open: false}">
								<button class="oh-btn oh-stop-prop oh-accordion-meta__btn" @click="open = !open"
									@click.outside="open = false" title="Action">
									{% trans "Actions" %}
									<ion-icon class="ms-2 oh-accordion-meta__btn-icon md hydrated" name="caret-down-outline"
										role="img" aria-label="caret down outline"></ion-icon>
								</button>
								<div class="oh-dropdown__menu oh-dropdown__menu--right" x-show="open" style="display: none;">
									<ul class="oh-dropdown__items">
										<li class="oh-dropdown__item">
											<a class="oh-dropdown__link" data-toggle="oh-modal-toggle"
												data-target="#objectCreateModal"
												hx-get="{% url 'document-request-update' document_list.list.0.document_request_id.id %}"
												hx-target="#objectCreateModalTarget">{% trans "Edit" %}</a>
										</li>
										<li class="oh-dropdown__item">
											<form action="{% url 'document-request-delete' document_list.list.0.document_request_id.id %}"
												onsubmit="return confirm('{% trans " Are you sure you want to delete this Document Request?" %}');"
												class="w-50" method='post'>
												{% csrf_token %}
												<button type="submit" class="oh-dropdown__link oh-dropdown__link--danger"
													title="{% trans 'Delete' %}">
													{% trans "Delete" %}
												</button>
											</form>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
					<div class="oh-accordion-meta__body d-none">
						<div class="oh-sticky-table oh-sticky-table--no-overflow mb-5">
							<div class="oh-card p-4">
								{% for document in document_list.list %}
									<div class="oh-user_permission-list_item accordion exclude-accordion-style "
										hx-get='{% url "view-file" document.id %}' hx-target="#viewFile"
										data-toggle="oh-modal-toggle" id="requestDocument{{document.id}}"
										data-target="#viewFileModal">
										<div class="oh-user_permission-list_profile ps-2 {% if document.status == 'approved' %}row-status--yellow{% elif document.status == 'rejected' %}row-status--red {% elif document.status == 'requested' %}row-status--blue{% endif %}">
											<input type="checkbox" id="{{ document.id }}" onchange="highlightRow($(this))"
												class="oh-input payslip-checkbox oh-input__checkbox all-documents-row"
												onclick="event.stopPropagation()" data-status="{{document.status}}">
											<div class="oh-navbar__user-photo oh-user_permission--profile">
												{% if document.document %}
													{% if document.status == "approved" %}
														<span class="oh-badge oh-badge--secondary oh-badge--small oh-badge--round ms-2 mr-2 file-upload"
															title="{% trans 'Approved' %}">
															<ion-icon name="checkmark"></ion-icon>
														</span>
													{% elif document.status == 'rejected' %}
														<span class="oh-badge oh-badge--secondary oh-badge--small oh-badge--round ms-2 mr-2 file-upload"
															title="{% trans 'Rejected' %}">
															<ion-icon name="alert"></ion-icon>
														</span>
													{% else %}
														<span
															class="oh-badge oh-badge--secondary oh-badge--small oh-badge--round ms-2 mr-2 file-upload"
															title="{% trans 'File Uploaded' %}">
															<ion-icon name="image-outline"></ion-icon>
														</span>
													{% endif %}
												{% else %}
													<span
														class="oh-badge oh-badge--secondary oh-badge--small oh-badge--round ms-2 mr-2 file-upload"
														hx-get="{% url 'file-upload' document.id %}"
														hx-target="#objectCreateModalTarget" data-document-id="{{ document.id }}"
														data-toggle="oh-modal-toggle" data-target="#objectCreateModal"
														onclick="event.stopPropagation()" title="{% trans 'Upload' %}">
														<ion-icon class="md hydrated m-0" name="add-outline" role="img"
															aria-label="add outline">
														</ion-icon>
													</span>
												{% endif %}
											</div>
											<div class="oh-feedback-card__name-container ms-1">
												<span class="oh-card__title oh-card__title--sm fw-bold me-1">{{document.title}} --
													{{document.employee_id.get_full_name}}
												</span>
												<span class="oh-user_permission_list-text oh-text--light"
													title="{{document.document_request_id.description}}">
													{{document.document_request_id.description|truncatechars:20}}
												</span>
											</div>
										</div>
										<div class="oh-btn-group">
											{% if perms.horilla_document.change_documentrequest %}
												{% if document.status == "approved" or not document.document %}
													<a class="oh-btn oh-btn--success w-100 oh-btn--disabled"
														onclick="event.stopPropagation()">
														<ion-icon class="me-1" name="checkmark-outline"></ion-icon>
													</a>
												{% else %}
													<a type="submit" hx-confirm="{% trans 'Do you want to approve this request' %}"
														hx-get="{% url 'document-approve' document.id %}" hx-target="#viewFile"
														title="{% trans 'Approve' %}" class="oh-btn oh-btn--success w-100"
														onclick="event.stopPropagation()">
														<ion-icon class="me-1" name="checkmark-outline"></ion-icon>
													</a>
												{% endif %}
												{% if document.status == 'rejected' or not document.document %}
													<a class="oh-btn oh-btn--danger w-100 oh-btn--disabled"
														onclick="event.stopPropagation()">
														<ion-icon class="me-1" name="close-circle-outline"></ion-icon>
													</a>
												{% else %}
													<a type="submit" hx-get="{% url 'document-reject' document.id %}"
														hx-target="#rejectFileForm" data-toggle="oh-modal-toggle"
														data-target="#rejectFileModal" title="{% trans 'Reject' %}"
														class="oh-btn oh-btn--danger w-100" onclick="event.stopPropagation()">
														<ion-icon class="me-1" name="close-circle-outline"></ion-icon>
													</a>
												{% endif %}
											{% endif %}
											{% if perms.horilla_document.delete_documentrequest %}
												<form hx-confirm="{% trans 'Are you sure you want to delete this Document Request?' %}"
													hx-post="{% url 'document-delete' document.id %}"
													hx-target="#requestDocument{{document.id}}" hx-swap="outerHTML"
													onclick="event.stopPropagation()">
													{% csrf_token %}
													<button type="submit" class="oh-btn  oh-btn--secondary"
														title="{% trans 'Delete' %}">
														<ion-icon class="me-1 md hydrated" name="trash-outline" role="img"
															aria-label="trash outline"></ion-icon>
													</button>
												</form>
											{% endif %}
										</div>
									</div>
								{% endfor %}
							</div>
						</div>
						<div class="oh-pagination">
							<span class="oh-pagination__page">
								{% trans "Page" %} {{ document_list.list.number }}
								{%trans "of" %} {{document_list.list.paginator.num_pages }}.
							</span>
							<nav class="oh-pagination__nav">
								<div class="oh-pagination__input-container me-3">
									<span class="oh-pagination__label me-1">{% trans "Page" %}</span>
									<input type="number" name="{{document_list.dynamic_name}}" class="oh-pagination__input"
										value="{{document_list.list.number}}"
										hx-get="{% url 'document-request-filter-view' %}?{{pd}}" hx-target="#view-container"
										min="1" />
									<span class="oh-pagination__label">{% trans "of" %}
										{{document_list.list.paginator.num_pages}}</span>
								</div>
								<ul class="oh-pagination__items">
									{% if document_list.list.has_previous %}
									<li class="oh-pagination__item oh-pagination__item--wide">
										<a hx-target="#view-container"
											hx-get="{% url 'document-request-filter-view' %}?{{pd}}&{{document_list.dynamic_name}}=1"
											class="oh-pagination__link">{% trans "First" %}</a>
									</li>
									<li class="oh-pagination__item oh-pagination__item--wide">
										<a hx-target="#view-container"
											hx-get="{% url 'document-request-filter-view' %}?{{pd}}&{{document_list.dynamic_name}}={{ document_list.list.previous_page_number }}"
											class="oh-pagination__link">{% trans "Previous" %}</a>
									</li>
									{% endif %} {% if document_list.list.has_next %}
									<li class="oh-pagination__item oh-pagination__item--wide">
										<a hx-target="#view-container"
											hx-get="{% url 'document-request-filter-view' %}?{{pd}}&{{document_list.dynamic_name}}={{ document_list.list.next_page_number }}"
											class="oh-pagination__link">{% trans "Next" %}</a>
									</li>
									<li class="oh-pagination__item oh-pagination__item--wide">
										<a hx-target="#view-container"
											hx-get="{% url 'document-request-filter-view' %}?{{pd}}&{{document_list.dynamic_name}}={{ document_list.list.paginator.num_pages }}"
											class="oh-pagination__link">{% trans "Last" %}</a>
									</li>
									{% endif %}
								</ul>
							</nav>
						</div>
					</div>
				</div>
			</div>
		{% endfor %}

		<div class="oh-pagination">
			<span class="oh-pagination__page">
				{% trans "Page" %} {{ documents.number }} {% trans "of" %}
				{{ documents.paginator.num_pages }}.
			</span>
			<nav class="oh-pagination__nav">
				<div class="oh-pagination__input-container me-3">
					<span class="oh-pagination__label me-1">{% trans "Page" %}</span>
					<input type="number" name="page" class="oh-pagination__input" value="{{documents.number}}"
						hx-get="{% url 'document-request-filter-view' %}?{{pd}}" hx-target="#view-container" min="1" />
					<span class="oh-pagination__label">{% trans "of" %}
						{{documents.paginator.num_pages}}</span>
				</div>
				<ul class="oh-pagination__items">
					{% if documents.has_previous %}
					<li class="oh-pagination__item oh-pagination__item--wide">
						<a hx-target="#view-container" hx-get="{% url 'document-request-filter-view' %}?{{pd}}&page=1"
							class="oh-pagination__link">{% trans "First" %}</a>
					</li>
					<li class="oh-pagination__item oh-pagination__item--wide">
						<a hx-target="#view-container"
							hx-get="{% url 'document-request-filter-view' %}?{{pd}}&page={{ documents.previous_page_number }}"
							class="oh-pagination__link">{% trans "Previous" %}</a>
					</li>
					{% endif %} {% if documents.has_next %}
					<li class="oh-pagination__item oh-pagination__item--wide">
						<a hx-target="#view-container"
							hx-get="{% url 'document-request-filter-view' %}?{{pd}}&page={{ documents.next_page_number }}"
							class="oh-pagination__link">{% trans "Next" %}</a>
					</li>
					<li class="oh-pagination__item oh-pagination__item--wide">
						<a hx-target="#view-container"
							hx-get="{% url 'document-request-filter-view' %}?{{pd}}&page={{ documents.paginator.num_pages }}"
							class="oh-pagination__link">{% trans "Last" %}</a>
					</li>
					{% endif %}
				</ul>
			</nav>
		</div>
	</div>
{% else %}
	<!-- start of empty page -->
	<div class="oh-empty">
		<img src="{% static 'images/ui/search.svg' %}" class="oh-404__image" alt="Page not found. 404." />
		<h1 class="oh-empty__title">{% trans "No Records found." %}</h1>
		<p class="oh-empty__subtitle">{% trans "No documents found." %}</p>
	</div>
	<!-- end of empty page -->
{% endif %}
<script>
	$(document).ready(function () {
		$(".oh-accordion-meta__body").first().removeClass('d-none');
		$(".oh-accordion-meta__header").first().addClass('oh-accordion-meta__header--show');
	})
</script>
