{"name": "openhrms-core", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "npm run development", "development": "mix"}, "repository": {"type": "git", "url": "git+https://github.com/ArjunCybro/OpenHRMS-Dashboard.git"}, "author": "OpenHRMS", "license": "ISC", "bugs": {"url": "https://github.com/ArjunCybro/OpenHRMS-Dashboard/issues"}, "homepage": "https://github.com/ArjunCybro/OpenHRMS-Dashboard#readme", "devDependencies": {"laravel-mix": "^6.0.49", "sass": "^1.90.0", "sass-loader": "^12.1.0"}, "dependencies": {"alpinejs": "^3.10.5", "ionicons": "^7.1.0", "jquery": "^3.6.3", "jquery-ui": "^1.13.2", "jquery-ui-touch-punch": "^0.2.3", "js-datepicker": "^5.18.2", "select2": "^4.1.0-rc.0", "uuid": "^9.0.0"}}