{% load i18n %}
<div>
    <div class="oh-general__tab-target oh-profile-section mb-4" id="personal" data-select2-id="select2-data-personal">
        <div class="oh-profile-section__card" data-select2-id="select2-data-48-0df8">
            <form action="" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6 d-flex align-items-center">
                        <div class="oh-profile-section__edit-photo me-4 mb-3" data-toggle="oh-modal-toggle"
                            data-target="#uploadPhotoModal">
                            <img src="{{form.instance.get_avatar}}" class="oh-profile-section__avatar preview"
                                alt="Username" />
                        </div>
                    </div>
                </div>
                {{form.employee_profile.errors}}
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label" for="firstname">{% trans "First Name" %}</label>
                        {{form.employee_first_name}}
                        {{form.employee_first_name.errors}}
                    </div>
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label" for="lastname">{% trans "Last Name" %}</label>
                        {{form.employee_last_name}}
                        {{form.employee_last_name.errors}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label" for="email">{% trans "Email" %}</label>
                        {{form.email}} {{form.email.errors}}
                    </div>
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label" for="phone">{% trans "Phone" %}</label>
                        {{form.phone}} {{form.phone.errors}}
                    </div>
                </div>
                <div class="row" data-select2-id="select2-data-46-nz80">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6" style="position: relative">
                        <label class="oh-label" for="dob">{% trans "Date of Birth" %}</label>
                        {{form.dob}} {{form.dob.errors}}
                    </div>
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6" data-select2-id="select2-data-45-37n6">
                        <label class="oh-label" for="gender">{% trans "Gender" %}</label>
                        {{form.gender}} {{form.gender.errors}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="{{form.qualification.id_for_label}}">
                            {% trans "Qualification" %}
                        </label>
                        {{form.qualification}} {{form.qualification.errors}}
                    </div>
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="{{form.experience.id_for_label}}">
                            {% trans "Experience" %}
                        </label>
                        {{form.experience}} {{form.experience.errors}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                        <label class="oh-label" for="address">{% trans "Address" %}</label>
                        {{form.address}} {{form.address.errors}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label" for="{{ form.country.id_for_label }}">{% trans "Country" %}</label>
                        <select class="oh-select-2 oh-select w-100" name="country" id="{{ form.country.id_for_label }}"
                            data-selected="{{ form.country.value|default:'' }}">
                        </select>
                        {{ form.country.errors }}
                    </div>
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="{{ form.state.id_for_label }}">{% trans "State" %}</label>
                        <select name="state" class="oh-select oh-select-2" id="{{ form.state.id_for_label }}"
                            data-selected="{{ form.state.value|default:'' }}">
                        </select>
                        {{ form.country.errors }}
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="state">{% trans "City" %}</label>
                        {{form.city}} {{form.city.errors}}
                    </div>
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="state">{% trans "Zip Code" %}</label>
                        {{form.zip}} {{form.zip.errors}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="state">{% trans "Emergency Contact" %}</label>
                        {{form.emergency_contact}}
                        {{form.emergency_contact.errors}}
                    </div>
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="emergencyContactName">{% trans "Contact Name" %}</label>
                        {{form.emergency_contact_name}}
                        {{form.emergency_contact_name.errors}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="emergencyContactRelation">
                            {% trans "Emergency Contact Relation" %}
                        </label>
                        {{form.emergency_contact_relation}}
                        {{form.emergency_contact_relation.errors}}
                    </div>
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="marital_status">{% trans "Marital Status" %}</label>
                        {{form.marital_status}} {{form.marital_status.errors}}
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                        <label class="oh-label d-block" for="children">{% trans "Children" %}</label>
                        {{form.children}} {{form.children.errors}}
                    </div>
                </div>
                <hr class="mt-5 mb-3" />
                <div class="w-100 d-flex align-items-center justify-content-end">
                    <button type="submit" class="oh-btn oh-btn--secondary oh-btn--w-100-resp">
                        {% trans "Save" %}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="oh-modal" id="uploadPhotoModal" role="dialog" aria-labelledby="uploadPhotoModal" aria-hidden="true">
        <div class="oh-modal__dialog">
            <div class="oh-modal__dialog-header">
                <span class="oh-modal__dialog-title" id="uploadPhotoModalLabel">{% trans "Upload Photo" %}</span>
                <button class="oh-modal__close" aria-label="Close">
                    <ion-icon name="close-outline"></ion-icon>
                </button>
            </div>
            <div class="oh-modal__dialog-body" id="uploadPhotoModalBody">
                <div class="oh-profile-section__image-container">
                    <form hx-post="{% url 'update-own-profile-image' %}" hx-target="#uploadPhotoModalBody"
                        hx-encoding="multipart/form-data" id="file-form">
                        {% csrf_token %}
                        <div class="oh-profile-section__modal-avatar">
                            <img src="{{form.instance.get_avatar}}" class="oh-profile-section__modal-image preview"
                                alt="Username" />
                        </div>
                        <input type="file" name="employee_profile" id="id_employee_profile"
                            class="oh-input oh-input--file oh-input--file-sm mt-4" />
                        <div class="d-flex justify-content-between w-100 align-items-center mt-4">
                            <button class="oh-btn oh-btn--light-danger mr-1" hx-target="#personalMessage"
                                hx-delete="{% url 'remove-own-profile-image' %}">
                                <ion-icon class="me-1" name="trash-outline"></ion-icon>
                                {% trans "Delete Image" %}
                            </button>
                            <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
                                {% trans "Update Image" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div id="personalMessage"></div>
        <script>
            $(document).ready(function () {
                $("#id_employee_profile").change(function (e) {
                    const file = this.files[0];
                    const reader = new FileReader();

                    reader.addEventListener("load", function () {
                        const imageUrl = reader.result;
                        $(".preview").attr("src", imageUrl);
                    });

                    reader.readAsDataURL(file);
                });
                $("#file-form").submit(function (e) {
                    e.preventDefault();

                    var formData = new FormData($(this)[0]);

                    $.ajax({
                        url: '{% url "update-own-profile-image" %}',
                        type: "POST",
                        data: formData,
                        async: false,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (response) {
                            $("#personalMessage").html(response);
                        },
                        error: function (xhr, status, error) {
                            // console.log(xhr.responseText);
                        },
                    });

                    return false;
                });
            });
        </script>
    </div>
</div>
