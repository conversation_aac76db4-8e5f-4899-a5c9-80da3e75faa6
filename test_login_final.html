<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login Test - Horilla Dashboard</title>
    <style>
        /* Final login page styles matching screenshot exactly */
        body.login-page {
            background-color: #f8f9fa !important;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .oh-auth {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #f8f9fa;
            padding: 2rem 1rem;
        }

        .oh-auth-card {
            background-color: white;
            width: 100%;
            max-width: 420px;
            padding: 3rem 2.5rem 2.5rem 2.5rem;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.04);
            border: none;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            text-align: center;
            letter-spacing: -0.025em;
        }

        .login-subtitle {
            color: #6c757d;
            font-size: 1rem;
            text-align: center;
            margin-bottom: 2.5rem;
            font-weight: 400;
            line-height: 1.4;
        }

        .login-form-group {
            margin-bottom: 1.5rem;
        }

        .login-label {
            display: block;
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .login-input {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 1px solid #e0e6ed;
            border-radius: 6px;
            font-size: 1rem;
            box-sizing: border-box;
            transition: all 0.2s ease-in-out;
            background-color: #fff;
        }

        .login-input:focus {
            outline: none;
            border-color: #4dabf7;
            box-shadow: 0 0 0 3px rgba(77, 171, 247, 0.1);
        }

        .login-input::placeholder {
            color: #adb5bd;
            font-weight: 400;
        }

        .login-password-container {
            position: relative;
        }

        .login-password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: color 0.2s ease;
            font-size: 1.25rem;
        }

        .login-password-toggle:hover {
            color: #495057;
        }

        .login-submit-btn {
            width: 100%;
            padding: 1rem;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .login-submit-btn:hover {
            background-color: #c0392b;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
        }

        .login-note {
            text-align: center;
            font-size: 0.9rem;
            color: #007bff;
            margin-bottom: 1.25rem;
            line-height: 1.5;
            font-weight: 400;
        }

        .login-forgot-link {
            display: block;
            text-align: center;
            color: #e74c3c;
            font-size: 0.95rem;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .login-forgot-link:hover {
            color: #c0392b;
            text-decoration: underline;
        }

        .login-logo {
            margin-top: 3rem;
            text-align: center;
        }

        .login-logo img {
            max-width: 100px;
            opacity: 0.4;
            filter: grayscale(100%);
        }

        /* Mobile responsive */
        @media (max-width: 480px) {
            .oh-auth-card {
                padding: 1.5rem 1rem;
                margin: 1rem 0.5rem;
            }
            
            .login-title {
                font-size: 1.75rem;
            }
            
            .login-subtitle {
                font-size: 0.9rem;
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body class="login-page">
    <div id="main">
        <main class="oh-auth">
            <div class="oh-auth-card">
                <h1 class="login-title">Sign In</h1>
                <p class="login-subtitle">Please login to access the dashboard.</p>
                <form>
                    <div class="login-form-group">
                        <label class="login-label" for="username">Username</label>
                        <input type="text" id="username" name="username" class="login-input"
                            placeholder="admin" value="admin" />
                    </div>
                    <div class="login-form-group">
                        <label class="login-label" for="password">Password</label>
                        <div class="login-password-container">
                            <input type="password" id="password" name="password"
                                class="login-input" placeholder="•••••" />
                            <button type="button" class="login-password-toggle" onclick="togglePassword()">
                                👁️
                            </button>
                        </div>
                    </div>
                    <button type="submit" class="login-submit-btn">
                        🔒 Secure Sign-in
                    </button>
                    <div class="login-note">
                        Note: You can use the username 'admin' and password 'admin' to log in.
                    </div>
                    <a href="#" class="login-forgot-link">Forgot password?</a>
                </form>
            </div>
            <div class="login-logo">
                <div style="color: #999; font-size: 1.5rem; font-weight: 300; opacity: 0.4;">Horilla</div>
            </div>
        </main>
    </div>
    
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const eyeIcon = document.querySelector('.login-password-toggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.innerHTML = '🙈';
            } else {
                passwordField.type = 'password';
                eyeIcon.innerHTML = '👁️';
            }
        }
    </script>
</body>
</html>
