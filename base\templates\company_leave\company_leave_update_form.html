{% load i18n %} {% if messages %}
<span hx-trigger="load" hx-target="#companyLeave" hx-get="{% url 'company-leave-filter' %}" hx-on-htmx-after-request="setTimeout(() => {
            $('.oh-modal__close').click();
        }, 1000);">
</span>
{% endif %} {% if form.errors %}
<div class="oh-wrapper">
    <div class="oh-alert-container">
        {% for error in form.non_field_errors %}
        <div class="oh-alert oh-alert--animated oh-alert--danger">
            {{ error }}
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}
<div class="oh-modal__dialog-header pb-0">
    <span class="oh-modal__dialog-title" id="editDialogDialog">{% trans "Update" %} {{form.verbose_name}}</span>
    <button class="oh-modal__close" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body pt-1">
    <form hx-post="{% url 'company-leave-update' id %}" hx-target="#objectCreateModalTarget"
        class="oh-profile-section pt-1">
        <label class="oh-label d-block" for="{{form.based_on_week.id_for_label}}">
            {{form.based_on_week.label}}
        </label>
        {{form.based_on_week}} {{form.based_on_week.errors}}
        <label class="oh-label d-block" for="{{form.based_on_week_day.id_for_label}}">
            {{form.based_on_week_day.label}}
        </label>
        {{form.based_on_week_day}} {{form.based_on_week_day.errors}}
        <label class="oh-label d-block" for="{{form.company_id.id_for_label}}">
            {{form.company_id.label}}
        </label>
        {{form.company_id}} {{form.company_id.errors}}
        <div class="oh-modal__dialog-footer p-0 mt-3">
            <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
                {% trans "Save" %}
            </button>
        </div>
    </form>
</div>
<script>
    var week_select = $("#objectUpdateModalTarget #id_based_on_week");
    week_select
        .find("option")
        .filter(function () {
            return $(this).text() === "---------";
        })
        .text("All");
</script>
