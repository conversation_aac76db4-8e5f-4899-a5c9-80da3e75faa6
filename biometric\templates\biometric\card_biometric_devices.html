{% load static %} {% load i18n %} {% load basefilters %}
{% include 'filter_tags.html' %}

{% if messages %}
    <div class="oh-wrapper">
        {% for message in messages %}
            <div class="oh-alert-container">
                <div class="oh-alert oh-alert--animated {{message.tags}}">
                    {{ message }}
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}

{% if devices %}
    <div class="oh-layout--grid-3">
        {% for device in devices %}
            <div class="oh-kanban-card oh-kanban-card--biometric {% if device.is_live %} oh-kanban-card--orange {% elif device.is_scheduler %} oh-kanban-card--blue {% else %} oh-kanban-card--red {% endif %}">
                <div class="oh-kanban-card__details">
                    <span class="oh-kanban-card__title">{{ device.name }}</span>
                    <span class="oh-kanban-card__subtitle d-block">{{ device.get_machine_type_display }}</span>
                    <span class="oh-kanban-card__subtitle d-block">{{ device.get_device_direction_display }}</span>
                    {% if device.machine_type == "zk" or device.machine_type == "cosec" or device.machine_type == "dahua" %}
                        <span class="oh-kanban-card__subtitle d-block">{{ device.machine_ip }}</span>
                        <span class="oh-kanban-card__subtitle d-block">{{ device.port }}</span>
                    {% else %}
                        <span class="oh-kanban-card__subtitle d-block">{{ device.api_url }}</span>
                    {% endif %}
                    <table>
                        <tr>
                            {% if device.machine_type == "zk" or device.machine_type == "cosec" %}
                            <td>
                                <span class="oh-kanban-card__subtitle d-block">{% trans "Activate live capture mode" %}</span>
                            </td>
                            <td>
                                <div class="oh-switch">
                                    <input type="checkbox" class="style-widget oh-switch__checkbox is-live-activate" {% if device.is_live %} checked title="{% trans 'Deactivate' %}" {% else %}
                                        title="{% trans 'Activate' %}" {% endif %} data-toggle="oh-modal-toggle"
                                        data-target="#BiometricDeviceTestModal" name="is_live" hx-trigger="change"
                                        hx-get="{% url 'biometric-device-live-capture' %}?deviceId={{ device.id }}&{{ pd }}&page={{ devices.number }}&view=card"
                                        hx-target="#BiometricDeviceTestFormTarget" />
                                </div>
                            </td>
                            {% endif %}
                        </tr>
                    </table>
                </div>
                <div class="oh-kanban-card__dots">
                    <div class="oh-dropdown" x-data="{show: false}">
                        <button class="oh-btn oh-btn--transparent text-muted p-3" @click="show = !show">
                            <ion-icon name="ellipsis-vertical-sharp"></ion-icon>
                        </button>
                        <div class="oh-dropdown__menu oh-dropdown__menu--dark-border oh-dropdown__menu--right" x-show="show"
                            @click.outside="show = false">
                            <ul class="oh-dropdown__items">
                                <li class="oh-dropdown__item">
                                    <a hx-get="{% url 'biometric-device-edit' device.id %}" class="oh-dropdown__link"
                                        data-toggle="oh-modal-toggle" data-target="#objectUpdateModal"
                                        hx-target="#objectUpdateModalTarget">{% trans "Edit" %}</a>
                                </li>
                                <li class="oh-dropdown__item">
                                    <a href="#" hx-get="{% url 'biometric-device-fetch-logs' device.id %}"
                                        data-toggle="oh-modal-toggle" data-target="#BiometricDeviceTestModal"
                                        hx-target="#BiometricDeviceTestFormTarget" class="oh-dropdown__link">
                                        {% trans "Fetch Logs" %}
                                    </a>
                                </li>
                                {% if perms.biometric.change_biometricdevices %}
                                    {% if device.is_active %}
                                        <li class="oh-dropdown__item">
                                            <a hx-on:click="hxConfirm(this,'Are you sure you want to archive this device?')" hx-trigger="confirmed"
                                                hx-swap="outerHTML" hx-select="#biometricDeviceList" hx-target="#biometricDeviceList"
                                                hx-post="{% url 'biometric-device-archive' device.id %}?{{ pd }}&page={{ devices.number }}&view=card"
                                                class="oh-dropdown__link">{% trans "Archive" %}
                                            </a>
                                        </li>
                                    {% else %}
                                    <li class="oh-dropdown__item">
                                        <a hx-on:click="hxConfirm(this,'Are you sure you want to un-archive this device?')" hx-trigger="confirmed"
                                            hx-swap="outerHTML" hx-select="#biometricDeviceList" hx-target="#biometricDeviceList"
                                            hx-post="{% url 'biometric-device-archive' device.id %}?{{ pd }}&page={{ devices.number }}&view=card"
                                            class="oh-dropdown__link">{% trans "Un-Archive" %}
                                        </a>
                                    </li>
                                    {% endif %}
                                {% endif %}
                                {% if perms.biometric.delete_biometricdevices %}
                                    <li class="oh-dropdown__item">
                                        <button class="oh-dropdown__link oh-dropdown__link--danger"  hx-trigger="confirmed"
                                            hx-post="{% url 'biometric-device-delete' device.id %}?{{ pd }}&page={{ devices.number }}&view=card"
                                            hx-swap="outerHTML" hx-select="#biometricDeviceList" hx-target="#biometricDeviceList"
                                            hx-on:click="hxConfirm(this,'Are you sure you want to delete this device?')">
                                            {% trans "Delete" %}
                                        </button>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="d-block oh-kanban-card__biometric-actions" style="margin-top: 53px;display: flex !important;justify-content: space-between;">
                    <div>
                        <a href="#" hx-get="{% url 'biometric-device-test' device.id %}" data-toggle="oh-modal-toggle"
                            data-target="#BiometricDeviceTestModal" hx-target="#BiometricDeviceTestFormTarget"
                            class="oh-checkpoint-badge text-success">{% trans "Test" %}
                        </a>
                        {% if device.is_scheduler %}
                            <a hx-on:click="hxConfirm(this,'Are you sure you want to unschedule the device attendance fetching?')"
                                hx-post="{% url 'biometric-device-unschedule' device.id %}?{{ pd }}&page={{ devices.number }}&view=card"
                                hx-trigger="confirmed" hx-swap="outerHTML" hx-select="#biometricDeviceList"
                                hx-target="#biometricDeviceList" class="oh-checkpoint-badge text-info">{% trans "Unschedule" %}
                            </a>
                        {% else %}
                            <a href="#" class="oh-checkpoint-badge text-info"
                                hx-get="{% url 'biometric-device-schedule' device.id %}" data-toggle="oh-modal-toggle"
                                data-target="#BiometricDeviceModal" hx-target="#BiometricDeviceFormTarget">{% trans "Schedule" %}
                            </a>
                        {% endif %}
                        {% if device.machine_type == "zk" or device.machine_type == "cosec" or device.machine_type == "dahua" or device.machine_type == "etimeoffice" %}
                            <a href="{% url 'biometric-device-employees' device.id %}"
                                class="oh-checkpoint-badge text-secondary bio-user-list">{% trans "Employee" %}
                            </a>
                        {% endif %}
                    </div>
                    <input type="checkbox" class="oh-input oh-input__checkbox biometric-device-instance" value="{{ device.id }}"
                        hx-on:click="this.closest('.oh-kanban-card--biometric').classList.toggle('highlight-selected')" />
                </div>
            </div>
        {% endfor %}
    </div>
    <div class="oh-pagination" data-pd="{{ pd }}">
        <span class="oh-pagination__page" data-toggle="modal">
            {% trans "Page" %} {{ devices.number }} {% trans "of" %} {{ devices.paginator.num_pages }}.
        </span>
        <nav class="oh-pagination__nav">
            <div class="oh-pagination__input-container me-3">
                <span class="oh-pagination__label me-1">{% trans "Page" %}</span>

                <input type="number" name="page" class="oh-pagination__input" value="{{ devices.number }}"
                    hx-get="{% url 'view-biometric-devices' %}?{{ pd }}&view=card" hx-swap="outerHTML"
                    hx-select="#biometricDeviceList" hx-target="#biometricDeviceList" min="1" />
                <span class="oh-pagination__label">{% trans "of" %} {{ devices.paginator.num_pages }}</span>
            </div>

            <ul class="oh-pagination__items">
                {% if devices.has_previous %}
                    <li class="oh-pagination__item oh-pagination__item--wide">
                        <a hx-swap="outerHTML" hx-select="#biometricDeviceList" hx-target="#biometricDeviceList"
                            hx-get="{% url 'view-biometric-devices' %}?{{ pd }}&page=1&view=card"
                            class="oh-pagination__link">{% trans "First" %}</a>
                    </li>
                    <li class="oh-pagination__item oh-pagination__item--wide">
                        <a hx-swap="outerHTML" hx-select="#biometricDeviceList" hx-target="#biometricDeviceList"
                            hx-get="{% url 'view-biometric-devices' %}?{{ pd }}&page={{ devices.previous_page_number }}&view=card"
                            class="oh-pagination__link">{% trans "Previous" %}</a>
                    </li>
                {% endif %}
                {% if devices.has_next %}
                    <li class="oh-pagination__item oh-pagination__item--wide">
                        <a hx-swap="outerHTML" hx-select="#biometricDeviceList" hx-target="#biometricDeviceList"
                            hx-get="{% url 'view-biometric-devices' %}?{{ pd }}&page={{ devices.next_page_number }}&view=card"
                            class="oh-pagination__link">{% trans "Next" %}
                        </a>
                    </li>
                    <li class="oh-pagination__item oh-pagination__item--wide">
                        <a hx-swap="outerHTML" hx-select="#biometricDeviceList" hx-target="#biometricDeviceList"
                            hx-get="{% url 'view-biometric-devices' %}?{{ pd }}&page={{ devices.paginator.num_pages }}&view=card"
                            class="oh-pagination__link">{% trans "Last" %}
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    <script>
        $(document).ready(function () {
            var pd = $(".oh-pagination").attr("data-pd");
            var hxVals = JSON.stringify(pd);
            $("#addBiometricDevice").attr("hx-vals", `{"pd":${hxVals}}`);

            $(".bio-user-list").click(function () {
                $("#BiometricDeviceTestModal").toggleClass("oh-modal--show");
            });
            $(".anviz-data-fetch").click(function (e) {
                $("#BiometricDeviceTestModal").toggleClass("oh-modal--show");
            });
        });
    </script>
    <div class="oh-modal" id="BiometricDeviceTestModal" role="dialog" aria-labelledby="BiometricDeviceTestModal"
        aria-hidden="true">
        <div class="oh-modal__dialog" style="max-width: 550px" id="BiometricDeviceTestFormTarget">
            {% include "animation.html" %}
        </div>
    </div>
{% else %}
<div class="oh-wrapper">
    <div class="oh-empty">
        <img src="{% static 'images/ui/search.svg' %}" class="oh-404__image" alt="Page not found. 404." />
        <h1 class="oh-empty__title">{% trans "No Records found." %}</h1>
        <p class="oh-empty__subtitle">{% trans "No biometric devices found." %}</p>
    </div>
</div>
{% endif %}
