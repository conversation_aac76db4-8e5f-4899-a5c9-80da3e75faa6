{% extends "index.html" %} {% block content %} {% load static %} {% load i18n %}
{% load basefilters %} {% load horillafilters %}
<style>
	.file-upload {
		margin-top: 6px !important;
		width: 34px !important;
		height: 34px !important;
		font-size: 1.75rem !important;
	}

	.custom-dialog {
		max-width: 1000px;
		max-height: 800px;
	}

	.oh-not-found {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		height: 50vh;
		opacity: 0.5;
	}

	.file-validation {
		color: #4f5bd9;
		background-color: #d8e7f0;
		border-color: #d6e9c6;
		padding: 15px;
		border: 1px solid transparent;
		border-radius: 4px;
	}
</style>
{% include 'documents/document_nav.html' %}
<div class="oh-checkpoint-badge mb-2" id="selectedDocuments" data-ids="[]" data-clicked="" style="display: none">
	{% trans "Selected Documents" %}
</div>
<div id="view-container" class="oh-wrapper">
	{% include 'documents/requests.html' %}
</div>


{% endblock content %}
