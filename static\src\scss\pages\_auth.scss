
// Login Page Styling to Match Screenshot Exactly
body.login-page {
    background-color: #f8f9fa !important;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.oh-auth {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: #f8f9fa;
    padding: 2rem 1rem;

    @media (max-width: 767.98px) {
        padding: 1rem;
    }
}

.oh-auth-card {
    background-color: white;
    width: 100%;
    max-width: 420px;
    padding: 3rem 2.5rem 2.5rem 2.5rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.04);
    border: none;

    @media (max-width: 767.98px) {
        padding: 2rem 1.5rem;
        max-width: 350px;
    }
}

// Typography matching screenshot
.login-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    text-align: center;
    letter-spacing: -0.025em;
}

.login-subtitle {
    color: #6c757d;
    font-size: 1rem;
    text-align: center;
    margin-bottom: 2.5rem;
    font-weight: 400;
    line-height: 1.4;
}

// Form styling to match screenshot
.login-form-group {
    margin-bottom: 1.5rem;
}

.login-label {
    display: block;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.login-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid #e0e6ed;
    border-radius: 6px;
    font-size: 1rem;
    box-sizing: border-box;
    transition: all 0.2s ease-in-out;
    background-color: #fff;

    &:focus {
        outline: none;
        border-color: #4dabf7;
        box-shadow: 0 0 0 3px rgba(77, 171, 247, 0.1);
    }

    &::placeholder {
        color: #adb5bd;
        font-weight: 400;
    }
}

// Password field with eye icon
.login-password-container {
    position: relative;

    .login-password-toggle {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
        transition: color 0.2s ease;

        &:hover {
            color: #495057;
        }

        &:focus {
            outline: none;
            color: #495057;
        }

        ion-icon {
            font-size: 1.25rem;
        }
    }
}

// Submit button matching screenshot (red/orange)
.login-submit-btn {
    width: 100%;
    padding: 1rem;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;

    &:hover {
        background-color: #c0392b;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.25);
    }

    &:active {
        transform: translateY(0);
    }

    ion-icon {
        font-size: 1.1rem;
    }
}

// Note text styling (blue text)
.login-note {
    text-align: center;
    font-size: 0.9rem;
    color: #007bff;
    margin-bottom: 1.25rem;
    line-height: 1.5;
    font-weight: 400;
}

// Forgot password link styling
.login-forgot-link {
    display: block;
    text-align: center;
    color: #e74c3c;
    font-size: 0.95rem;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;

    &:hover {
        color: #c0392b;
        text-decoration: underline;
    }

    &:focus {
        outline: none;
        color: #c0392b;
        text-decoration: underline;
    }
}

// Logo styling at bottom
.login-logo {
    margin-top: 3rem;
    text-align: center;

    img {
        max-width: 100px;
        opacity: 0.4;
        filter: grayscale(100%);
    }
}

// Company name styling
.login-company-name {
    font-size: 1.1rem;
    color: #6c757d;
    text-align: center;
    margin-top: 0.75rem;
    margin-bottom: 0;
    font-weight: 300;
}

// Alert container styling
.oh-alert-container {
    position: fixed;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    width: 90%;
    max-width: 400px;
}

.oh-alert {
    padding: 0.875rem 1rem;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;

    &.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    &.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    &.warning {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
}

// Additional responsive enhancements
@media (max-width: 480px) {
    .oh-auth-card {
        padding: 1.5rem 1rem;
        margin: 1rem 0.5rem;
    }

    .login-title {
        font-size: 1.75rem;
    }

    .login-subtitle {
        font-size: 0.9rem;
        margin-bottom: 2rem;
    }

    .login-input {
        padding: 0.75rem 0.875rem;
        font-size: 0.95rem;
    }

    .login-submit-btn {
        padding: 0.875rem;
        font-size: 0.95rem;
    }

    .login-note {
        font-size: 0.85rem;
    }

    .login-forgot-link {
        font-size: 0.9rem;
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .oh-auth-card {
        max-width: 450px;
        padding: 3.5rem 3rem 3rem 3rem;
    }
}

@media (min-width: 1025px) {
    .oh-auth-card {
        max-width: 480px;
        padding: 4rem 3.5rem 3.5rem 3.5rem;
    }

    .login-title {
        font-size: 2.25rem;
    }

    .login-subtitle {
        font-size: 1.05rem;
    }
}

