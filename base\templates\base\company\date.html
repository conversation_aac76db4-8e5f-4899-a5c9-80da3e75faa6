{% extends 'settings.html' %}
{% load i18n %}
{% load static %}
{% block settings %}

<div class="oh-inner-sidebar-content__header mt-3">
    <h2 class="oh-inner-sidebar-content__title">{% trans "Date Format" %}</h2>
</div>
{% if perms.base.change_company %}

        <label for="" class="settings-label mb-1">{% trans "Select Date Format:" %}</label>
        <div class="w-75 d-flex">
            <select id="dateFormat" class="oh-select oh-select-2">
                <option value="">--------------</option>
                <option value="DD-MM-YYYY">{% trans "Day, Month, Year (e.g., 30-12-2023)" %}</option>
                <option value="DD.MM.YYYY">{% trans "Day, Month, Year (e.g., 30.12.2023)" %}</option>
                <option value="DD/MM/YYYY">{% trans "Day, Month, Year (e.g., 30/12/2023)" %}</option>
                <option value="MM/DD/YYYY">{% trans "Month, Day, Year (e.g., 12/30/2023)" %}</option>
                <option value="YYYY-MM-DD">{% trans "Year, Month, Day (e.g., 2023-12-30)" %}</option>
                <option value="YYYY/MM/DD">{% trans "Year, Month, Day (e.g., 2023/12/30)" %}</option>
                <option value="MMMM D, YYYY">{% trans "Full Month Name, Day, Year (e.g., January 1, 2023)" %}</option>
                <option value="DD MMMM, YYYY">{% trans "Day, Full Month Name, Year (e.g., 1 January, 2023)" %}</option>
                <option value="MMM. D, YYYY">{% trans "Abbreviated Month Name, Day, Year (e.g., Jan. 1, 2023)" %}</option>
                <option value="D MMM. YYYY">{% trans "Day, Abbreviated Month Name, Year (e.g., 1 Jan. 2023)" %}</option>
                <option value="dddd, MMMM D, YYYY">{% trans "Full Day Name, Full Month Name, Day, Year (e.g., Monday, January 1, 2023)" %}</option>
            </select>
            <div class="w-100">
                <button onclick="saveDateFormat()"
                class="oh-btn oh-btn--secondary ms-3 oh-btn--w-100-resp" style="height: 48px;"
                >{% trans "Save Date Format" %}</button>
            </div>
        </div>

{% endif %}

<div style=" font-size: 20px; color: hsl(8,77%,56%); margin-top:20px; margin-bottom:80px ;">
    <b>{% trans "Current Date Format"%} &nbsp :</b> &nbsp
    <span id="dateDisplay" style="font-size: 18px; color: black;" class='mt-3'></span>
</div>

<hr>

<!-- Time format selection-->

<div class="oh-inner-sidebar-content__header" style="margin-top:80px">
    <h2 class="oh-inner-sidebar-content__title">{% trans "Time Format" %}</h2>
</div>

{% if perms.base.change_company %}

<label for="timeFormat" class="settings-label mb-1">{% trans "Select Time Format:" %}</label>
<div class="w-75 d-flex">
    <select id="timeFormat" class="oh-select oh-select-2">
        <option value="">--------------</option>
        <option value="hh:mm A">{% trans "12 Hr. (e.g., 06:00 AM or 06:00 PM)" %}</option>
        <option value="HH:mm">{% trans "24 Hr. (e.g., 06:00 or 18:00)" %}</option>
    </select>
    <div class="w-100">
        <button onclick="saveTimeFormat()"
            class="oh-btn oh-btn--secondary ms-3 oh-btn--w-100-resp" style="height: 48px;"
        >{% trans "Save Time Format" %}</button>
    </div>
</div>
{% endif %}
<script>
    $("#dateFormat").val("{{request.user.employee_get.employee_work_info.company_id.date_format}}").change();
    $("#timeFormat").val("{{request.user.employee_get.employee_work_info.company_id.time_format}}").change();
</script>
<div style="font-size: 20px; color: hsl(8, 77%, 56%); margin-top: 20px">
    <b>{% trans "Current Time Format" %} &nbsp :</b> &nbsp
    <span id="timeDisplay" style="font-size: 18px; color: black;" class='mt-3'></span>
</div>

<script src="{% static '/build/js/moment.js' %}"></script>
<script src="{% static 'base/date_formatting.js' %}"></script>
<script src="{% static 'base/date_settings.js' %}"></script>
<script src="{% static 'base/time_formatting.js' %}"></script>
<script src="{% static 'base/time_settings.js' %}"></script>

<script>

    // To display the selected date format
    const formatMapping = {
        'DD-MM-YYYY': '{% trans "Day, Month, Year (e.g., 30-12-2023)" %}',
        'DD.MM.YYYY': '{% trans "Day, Month, Year (e.g., 30.12.2023)" %}',
        'DD/MM/YYYY': '{% trans "Day, Month, Year (e.g., 30/12/2023)" %}',
        'MM/DD/YYYY': '{% trans "Month, Day, Year (e.g., 12/30/2023)" %}',
        'YYYY-MM-DD': '{% trans "Year, Month, Day (e.g., 2023-12-30)" %}',
        'YYYY/MM/DD': '{% trans "Year, Month, Day (e.g., 2023/12/30)" %}',
        'MMMM D, YYYY': '{% trans "Full Month Name, Day, Year (e.g., January 1, 2023)" %}',
        'DD MMMM, YYYY': '{% trans "Day, Full Month Name, Year (e.g., 1 January, 2023)" %}',
        'MMM. D, YYYY': '{% trans "Abbreviated Month Name, Day, Year (e.g., Jan. 1, 2023)" %}',
        'D MMM. YYYY': '{% trans "Day, Abbreviated Month Name, Year (e.g., 1 Jan. 2023)" %}',
        'dddd, MMMM D, YYYY': '{% trans "Full Day Name, Full Month Name, Day, Year (e.g., Monday, January 1, 2023)" %}',
    };

    // Get the current date
    var currentDate = new Date();

    // Format the current date according to the storedDateFormat
    var formattedDate = moment(currentDate).format(storedDateFormat);

    var selectedFormatDescription = formatMapping[storedDateFormat];

    document.getElementById('dateDisplay').textContent = `${selectedFormatDescription}`;

    // To display the selected time format
    const timeFormatMapping = {
        'hh:mm A': '{% trans "12 Hr. (e.g., 06:00 AM or 06:00 PM)" %}',
        'HH:mm': '{% trans "24 Hr. (e.g., 06:00 or 18:00)" %}',
    };

    // Get the current date and time
    var currentDateTime = new Date();

    // Format the current time according to the storedTimeFormat
    var formattedTime = moment(currentDateTime).format(storedTimeFormat);
    var selectedTimeFormatDescription = timeFormatMapping[storedTimeFormat];

    document.getElementById('timeDisplay').textContent = `${selectedTimeFormatDescription}`;

</script>
{% endblock %}
