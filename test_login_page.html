<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login Test - Horilla Dashboard</title>
    <style>
        /* Test styles based on the new login page design */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .login-page .oh-auth {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #f8f9fa;
            padding: 2rem 1rem;
        }
        
        .oh-auth-card {
            background-color: white;
            width: 100%;
            max-width: 400px;
            padding: 3rem 2.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .login-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            text-align: center;
        }
        
        .login-subtitle {
            color: #6c757d;
            font-size: 0.95rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-form-group {
            margin-bottom: 1.5rem;
        }
        
        .login-label {
            display: block;
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .login-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.95rem;
            box-sizing: border-box;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .login-input:focus {
            outline: none;
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .login-input::placeholder {
            color: #6c757d;
        }
        
        .login-password-container {
            position: relative;
        }
        
        .login-password-toggle {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 0.25rem;
        }
        
        .login-password-toggle:hover {
            color: #495057;
        }
        
        .login-submit-btn {
            width: 100%;
            padding: 0.875rem 1rem;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.15s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .login-submit-btn:hover {
            background-color: #c0392b;
        }
        
        .login-note {
            text-align: center;
            font-size: 0.85rem;
            color: #007bff;
            margin-bottom: 1rem;
            line-height: 1.4;
        }
        
        .login-forgot-link {
            display: block;
            text-align: center;
            color: #e74c3c;
            font-size: 0.9rem;
            text-decoration: none;
        }
        
        .login-forgot-link:hover {
            color: #c0392b;
            text-decoration: underline;
        }
        
        .login-logo {
            margin-top: 2rem;
            text-align: center;
        }
        
        .login-logo img {
            max-width: 120px;
            opacity: 0.6;
            filter: grayscale(100%);
        }
        
        .login-company-name {
            font-size: 1.125rem;
            color: #6c757d;
            text-align: center;
            margin-top: 0.5rem;
            margin-bottom: 0;
        }
    </style>
</head>
<body class="login-page">
    <div id="main">
        <main class="oh-auth">
            <div class="oh-auth-card">
                <h1 class="login-title">Sign In</h1>
                <p class="login-subtitle">Please login to access the dashboard.</p>
                <form>
                    <div class="login-form-group">
                        <label class="login-label" for="username">Username</label>
                        <input type="text" id="username" name="username" class="login-input"
                            placeholder="admin" value="admin" />
                    </div>
                    <div class="login-form-group">
                        <label class="login-label" for="password">Password</label>
                        <div class="login-password-container">
                            <input type="password" id="password" name="password"
                                class="login-input" placeholder="••••" />
                            <button type="button" class="login-password-toggle" onclick="togglePassword()">
                                👁️
                            </button>
                        </div>
                    </div>
                    <button type="submit" class="login-submit-btn">
                        🔒 Secure Sign-in
                    </button>
                    <div class="login-note">
                        Note: You can use the username 'admin' and password 'admin' to log in.
                    </div>
                    <a href="#" class="login-forgot-link">Forgot password?</a>
                </form>
            </div>
            <div class="login-logo">
                <div style="color: #999; font-size: 1.5rem; font-weight: 300;">Horilla</div>
            </div>
        </main>
    </div>
    
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const eyeIcon = document.querySelector('.login-password-toggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.innerHTML = '🙈';
            } else {
                passwordField.type = 'password';
                eyeIcon.innerHTML = '👁️';
            }
        }
    </script>
</body>
</html>
