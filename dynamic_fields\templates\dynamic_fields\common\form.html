{% load widget_tweaks %} {% load i18n %}
{% load generic_template_filters %}
<style>
  .condition-highlight {
    background-color: #ffa5000f;
  }
</style>
{% if form.verbose_name %}
<div class="oh-modal__dialog-header">
  <h2 class="oh-modal__dialog-title" id="createTitle">
  {{form.display_title}}
  </h2>
  <button type="button" class="oh-modal__close--custom" onclick="$(this).closest('.oh-modal--show').removeClass('oh-modal--show')" aria-label="Close" {{form.close_button_attrs|safe}}>
      <ion-icon name="close-outline" role="img" class="md hydrated" aria-label="close outline"></ion-icon>
  </button>
</div>
{% endif %}
<div class="oh-modal__dialog-body oh-modal__dialog-relative">
  {% if form.instance_ids %}
  <div class="oh-modal__dialog oh-modal__dialog--navigation m-0 p-0">
    <button
      hx-get="{{form.previous_url}}?{{form.ids_key}}={{form.instance_id}}&{{request.GET.urlencode}}"
      hx-swap="innerHTML"
      hx-target="#genericModalBody"
      class="oh-modal__diaglog-nav oh-modal__nav-prev"
    >
      <ion-icon name="chevron-back-outline"></ion-icon>
    </button>

    <button
      hx-get="{{form.next_url}}?{{form.ids_key}}={{form.instance_id}}&{{request.GET.urlencode}}"
      hx-swap="innerHTML"
      hx-target="#genericModalBody"
      class="oh-modal__diaglog-nav oh-modal__nav-next"
    >
      <ion-icon name="chevron-forward-outline"></ion-icon>
    </button>
  </div>
  {% endif %}
  <div class="oh-general__tab-target oh-profile-section" id="{{form.container_id}}">
    <div class="oh-profile-section__card row">
      <div class="row" style="padding-right: 0;">
        <div class="col-12" style="padding-right: 0;">{{ form.non_field_errors }}</div>
        {% for field in form.visible_fields %}
        <div class="col-12 col-md-{{field|col}}" id="id_{{ field.name }}_parent_div" style="padding-right: 0;">
          <div class="oh-label__info" for="id_{{ field.name }}">
            <label class="oh-label {% if field.field.required %} required-star{% endif %}" for="id_{{ field.name }}"
              >{% trans field.label %}</label
            >
            {% if field.help_text != '' %}
            <span
              class="oh-info mr-2"
              title="{{ field.help_text|safe }}"
            ></span>
            {% endif %}
          </div>

          {% if field.field.widget.input_type == 'checkbox' %}
          <div class="oh-switch" style="width: 30px">
            {{ field|add_class:'oh-switch__checkbox' }}
          </div>
          {% else %}
          <div id="dynamic_field_{{field.name}}">
            {{ field|add_class:'form-control' }}
            {{ field.errors }}
          </div>
          {% endif %}
        </div>
        {% endfor %}
      </div>

      {% for field in form.hidden_fields %} {{ field }} {% endfor %}

      <div class="d-flex flex-row-reverse">
        <button
          type="submit"
          class="oh-btn oh-btn--secondary mt-2 mr-0 pl-4 pr-5 oh-btn--w-100-resp"
          {{form.submit_button_attrs|safe}}
        >
          {% trans 'Save' %}
        </button>
      </div>
    </div>
  </div>
</div>
