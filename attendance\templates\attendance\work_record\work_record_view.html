{% extends 'index.html' %} {% block content %} {% load i18n %}

<style>
    table {
        width: 100%;
    }
    th,
    td {
        border: 1px solid hsl(213,22%,84%);
        padding: 5px;
    }

    th {
        background-color: lightgray;
    }
    .header {
        background:lightgray;
    }
    .holiday {
        background:#e3e3e8;
        border:none
    }
    .oh-card {
        background-color: #ededed;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 20px;
    }

    .oh-card h4 {
        color: #333;
        margin: 0;
        font-weight: bold;
    }
    .days {
        width:30px;
    }
    #workRecordTable a {
        color:inherit;
    }
    .oh-sticky-table__th {
        padding:5px !important;
        text-align: center;
    }
</style>

<div class="oh-wrapper">
    <div class="col-12 mt-4 mb-4">
        <div class="oh-card d-flex justify-content-between align-items-center">
            <div>
                <h4>{% trans "Work Records" %}</h4>
            </div>
            <div class="me-3" >
                <h6 class="mb-2 fw-bold">{% trans "Date:" %} {{ current_date }}</h6>
                <h6>
                    <div style = "cursor: pointer;display:flex;align-items:center">
                        <label for="monthYearField" class="text-danger fw-bold" style = "cursor: pointer;">{% trans "Month" %}</label>
                        <input class="oh-select p-2 w-75 m-1"
                        type="month"
                        id="monthYearField"
                        value="{{ current_date|date:"Y-m" }}"
                        name="month"
                        hx-get="{% url 'work-records-change-month' %}"
                        hx-target="#workRecordTable"
                        hx-trigger="change"
                        >
                    </div>
                </h6>
            </div>
        </div>
        <div id="workRecordTable" hx-get="{% url 'work-records-change-month' %}?month={{pd}}&{{ current_date|date:"Y-m" }}" hx-trigger="load">
            <div class="animated-background"></div>
        </div>
    </div>
</div>

{% endblock content %}
