// -----------------------------------------------------------------------------
// Global Dashboard Styling - Applied universally across all pages
// -----------------------------------------------------------------------------

// Global Dashboard Layout
.horilla-dashboard {
  font-family: $text-font-stack;
  background-color: $dashboard-bg-primary;
  color: $dashboard-text-primary;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  // Global container improvements
  .container-fluid,
  .container {
    padding-left: $dashboard-spacing-lg;
    padding-right: $dashboard-spacing-lg;
  }
}

// Enhanced Card System
.oh-card,
.oh-card-dashboard {
  background: $dashboard-bg-tertiary;
  border: 1px solid $dashboard-border-light;
  border-radius: $dashboard-radius-lg;
  box-shadow: $dashboard-shadow-sm;
  transition: all $dashboard-transition-normal;
  overflow: hidden;

  &:hover {
    box-shadow: $dashboard-shadow-md;
    transform: translateY(-2px);
  }

  // Card header improvements
  .oh-card__header,
  .card-header {
    background: linear-gradient(135deg, $dashboard-primary-light, darken($dashboard-primary-light, 5%));
    border-bottom: 1px solid $dashboard-border-light;
    padding: $dashboard-spacing-lg;
    font-weight: 600;
    color: $dashboard-text-primary;
  }

  // Card body improvements
  .oh-card__body,
  .card-body {
    padding: $dashboard-spacing-lg;
  }

  // Card footer improvements
  .oh-card__footer,
  .card-footer {
    background-color: $dashboard-bg-secondary;
    border-top: 1px solid $dashboard-border-light;
    padding: $dashboard-spacing-md $dashboard-spacing-lg;
  }
}

// Modern Button System
.btn,
.oh-btn {
  border-radius: $dashboard-radius-md;
  padding: $dashboard-spacing-sm $dashboard-spacing-lg;
  font-weight: 500;
  transition: all $dashboard-transition-fast;
  border: none;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: $dashboard-spacing-sm;

  // Primary button
  &.btn-primary,
  &.oh-btn--primary {
    background: linear-gradient(135deg, $dashboard-primary, darken($dashboard-primary, 10%));
    color: white;
    box-shadow: $dashboard-shadow-sm;

    &:hover {
      background: linear-gradient(135deg, darken($dashboard-primary, 5%), darken($dashboard-primary, 15%));
      box-shadow: $dashboard-shadow-md;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // Secondary button
  &.btn-secondary,
  &.oh-btn--secondary {
    background: $dashboard-bg-secondary;
    color: $dashboard-text-primary;
    border: 1px solid $dashboard-border-medium;

    &:hover {
      background: darken($dashboard-bg-secondary, 5%);
      border-color: $dashboard-border-dark;
    }
  }

  // Success button
  &.btn-success,
  &.oh-btn--success {
    background: linear-gradient(135deg, $dashboard-success, darken($dashboard-success, 10%));
    color: white;

    &:hover {
      background: linear-gradient(135deg, darken($dashboard-success, 5%), darken($dashboard-success, 15%));
    }
  }

  // Warning button
  &.btn-warning,
  &.oh-btn--warning {
    background: linear-gradient(135deg, $dashboard-warning, darken($dashboard-warning, 10%));
    color: white;

    &:hover {
      background: linear-gradient(135deg, darken($dashboard-warning, 5%), darken($dashboard-warning, 15%));
    }
  }

  // Danger button
  &.btn-danger,
  &.oh-btn--danger {
    background: linear-gradient(135deg, $dashboard-danger, darken($dashboard-danger, 10%));
    color: white;

    &:hover {
      background: linear-gradient(135deg, darken($dashboard-danger, 5%), darken($dashboard-danger, 15%));
    }
  }

  // Small button
  &.btn-sm,
  &.oh-btn--sm {
    padding: $dashboard-spacing-xs $dashboard-spacing-md;
    font-size: 0.875rem;
  }

  // Large button
  &.btn-lg,
  &.oh-btn--lg {
    padding: $dashboard-spacing-md $dashboard-spacing-xl;
    font-size: 1.125rem;
  }
}

// Enhanced Table Styling
.table,
.oh-table {
  background: $dashboard-bg-tertiary;
  border-radius: $dashboard-radius-md;
  overflow: hidden;
  box-shadow: $dashboard-shadow-sm;
  border: 1px solid $dashboard-border-light;

  thead {
    background: linear-gradient(135deg, $dashboard-primary-light, darken($dashboard-primary-light, 5%));
    
    th {
      border-bottom: 2px solid $dashboard-border-medium;
      padding: $dashboard-spacing-lg;
      font-weight: 600;
      color: $dashboard-text-primary;
      text-transform: uppercase;
      font-size: 0.875rem;
      letter-spacing: 0.5px;
    }
  }

  tbody {
    tr {
      transition: background-color $dashboard-transition-fast;

      &:hover {
        background-color: $dashboard-bg-secondary;
      }

      &:nth-child(even) {
        background-color: rgba($dashboard-border-light, 0.3);
      }

      td {
        padding: $dashboard-spacing-lg;
        border-bottom: 1px solid $dashboard-border-light;
        color: $dashboard-text-primary;
      }
    }
  }
}

// Enhanced Form Controls
.form-control,
.oh-input {
  border: 2px solid $dashboard-border-light;
  border-radius: $dashboard-radius-md;
  padding: $dashboard-spacing-sm $dashboard-spacing-md;
  font-size: 1rem;
  transition: all $dashboard-transition-fast;
  background: $dashboard-bg-tertiary;

  &:focus {
    outline: none;
    border-color: $dashboard-primary;
    box-shadow: 0 0 0 3px rgba($dashboard-primary, 0.1);
  }

  &::placeholder {
    color: $dashboard-text-muted;
  }
}

.form-group,
.oh-form-group {
  margin-bottom: $dashboard-spacing-lg;

  label {
    font-weight: 500;
    margin-bottom: $dashboard-spacing-sm;
    color: $dashboard-text-primary;
  }
}

// Enhanced Sidebar
.oh-sidebar {
  background: linear-gradient(180deg, $brand-color, darken($brand-color, 10%));
  box-shadow: $dashboard-shadow-lg;
  transition: all $dashboard-transition-normal;

  .oh-sidebar__nav {
    padding: $dashboard-spacing-md 0;

    .oh-sidebar__nav-item {
      margin: $dashboard-spacing-xs $dashboard-spacing-md;
      border-radius: $dashboard-radius-md;
      transition: all $dashboard-transition-fast;

      &:hover {
        background: rgba($white, 0.1);
        transform: translateX(5px);
      }

      &.active {
        background: linear-gradient(135deg, $dashboard-primary, darken($dashboard-primary, 10%));
        box-shadow: $dashboard-shadow-sm;
      }

      a {
        padding: $dashboard-spacing-md;
        border-radius: $dashboard-radius-md;
        text-decoration: none;
        color: $white;
        display: flex;
        align-items: center;
        gap: $dashboard-spacing-md;
      }
    }
  }
}

// Enhanced Navigation Bar
.oh-navbar {
  background: $dashboard-bg-tertiary;
  box-shadow: $dashboard-shadow-sm;
  border-bottom: 1px solid $dashboard-border-light;
  padding: $dashboard-spacing-md $dashboard-spacing-lg;

  .oh-navbar__brand {
    font-weight: 600;
    color: $dashboard-text-primary;
  }

  .oh-navbar__nav {
    display: flex;
    align-items: center;
    gap: $dashboard-spacing-lg;
  }
}

// Dashboard Stats Cards
.dashboard-stat-card {
  background: linear-gradient(135deg, $dashboard-bg-tertiary, darken($dashboard-bg-tertiary, 2%));
  border: 1px solid $dashboard-border-light;
  border-radius: $dashboard-radius-lg;
  padding: $dashboard-spacing-xl;
  text-align: center;
  transition: all $dashboard-transition-normal;
  box-shadow: $dashboard-shadow-sm;

  &:hover {
    transform: translateY(-5px);
    box-shadow: $dashboard-shadow-lg;
  }

  .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: $dashboard-primary;
    margin-bottom: $dashboard-spacing-sm;
  }

  .stat-label {
    color: $dashboard-text-secondary;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
  }

  .stat-icon {
    font-size: 3rem;
    color: $dashboard-primary;
    opacity: 0.7;
    margin-bottom: $dashboard-spacing-md;
  }
}

// Enhanced Alerts
.alert {
  border: none;
  border-radius: $dashboard-radius-md;
  padding: $dashboard-spacing-lg;
  margin-bottom: $dashboard-spacing-lg;
  box-shadow: $dashboard-shadow-sm;

  &.alert-success {
    background: linear-gradient(135deg, rgba($dashboard-success, 0.1), rgba($dashboard-success, 0.05));
    border-left: 4px solid $dashboard-success;
    color: darken($dashboard-success, 20%);
  }

  &.alert-warning {
    background: linear-gradient(135deg, rgba($dashboard-warning, 0.1), rgba($dashboard-warning, 0.05));
    border-left: 4px solid $dashboard-warning;
    color: darken($dashboard-warning, 20%);
  }

  &.alert-danger {
    background: linear-gradient(135deg, rgba($dashboard-danger, 0.1), rgba($dashboard-danger, 0.05));
    border-left: 4px solid $dashboard-danger;
    color: darken($dashboard-danger, 20%);
  }

  &.alert-info {
    background: linear-gradient(135deg, rgba($dashboard-primary, 0.1), rgba($dashboard-primary, 0.05));
    border-left: 4px solid $dashboard-primary;
    color: darken($dashboard-primary, 20%);
  }
}

// Modern Loading Spinner
.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid $dashboard-border-light;
  border-left: 4px solid $dashboard-primary;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive Design Enhancements
@media (max-width: map-get($breakpoints, 'medium')) {
  .oh-card,
  .oh-card-dashboard {
    margin-bottom: $dashboard-spacing-lg;
  }

  .dashboard-stat-card {
    .stat-number {
      font-size: 2rem;
    }
  }

  .container-fluid,
  .container {
    padding-left: $dashboard-spacing-md;
    padding-right: $dashboard-spacing-md;
  }
}