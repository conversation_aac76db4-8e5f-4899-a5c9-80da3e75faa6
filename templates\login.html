
{% load static %} {% load i18n %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - {{white_label_company_name}} Dashboard</title>
    <link rel="apple-touch-icon" sizes="180x180"
        href="{% if white_label_company.icon %}{{white_label_company.icon.url}} {% else %}{% static 'favicons/apple-touch-icon.png' %}{% endif %}">
    <link rel="icon" type="image/png" sizes="32x32"
        href="{% if white_label_company.icon %}{{white_label_company.icon.url}} {% else %}{% static 'favicons/favicon-32x32.png' %}{% endif %}">
    <link rel="icon" type="image/png" sizes="16x16"
        href="{% if white_label_company.icon %}{{white_label_company.icon.url}} {% else %}{% static 'favicons/favicon-16x16.png' %}{% endif %}">
    <link rel="stylesheet" href="{% static '/build/css/style.min.css' %}" />
    <link rel="manifest" href="{% static 'build/manifest.json' %}" />
</head>

<body class="login-page">
    <main class="oh-auth">
        <div class="oh-auth-card">
            <h1 class="login-title">Sign In</h1>
            <p class="login-subtitle">Please login to access the dashboard.</p>
            <form method="post">
                <div class="login-form-group">
                    <label class="login-label" for="username">Username</label>
                    <input type="text" id="username" name="username" class="login-input"
                        placeholder="admin" value="admin" />
                </div>
                <div class="login-form-group">
                    <label class="login-label" for="password">Password</label>
                    <div class="login-password-container">
                        <input type="password" id="password" name="password"
                            class="login-input" placeholder="••••" />
                        <button type="button" class="login-password-toggle" onclick="togglePassword()">
                            <ion-icon id="password-eye" name="eye-outline"></ion-icon>
                        </button>
                    </div>
                </div>
                <button type="submit" class="login-submit-btn">
                    <ion-icon name="lock-closed-outline"></ion-icon>
                    Secure Sign-in
                </button>
                <div class="login-note">
                    Note: You can use the username 'admin' and password 'admin' to log in.
                </div>
                <a href="{% url 'forgot-password' %}" class="login-forgot-link">
                    Forgot password?
                </a>
            </form>
        </div>
        <div class="login-logo">
            <img src="{% static 'images/ui/auth-logo.png' %}" alt="Horilla" />
        </div>
    </main>
    <script src="{% static '/build/js/web.frontend.min.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    {% comment %} <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script> {% endcomment %}
    <script type="module"  src="{% static 'images/ionicons/ui_icons/ionicons/ionicons.esm.js' %}"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const eyeIcon = document.getElementById('password-eye');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.name = 'eye-off-outline';
            } else {
                passwordField.type = 'password';
                eyeIcon.name = 'eye-outline';
            }
        }

        $(document).ready(function () {
            if ($(".oh-alert--warning").length > 0) {
                Swal.fire({
                    title: 'Access Denied !',
                    text: 'Your login credentials are currently blocked. Please contact HR administrators for assistance.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                });
            }
        });
    </script>
</body>

</html>
