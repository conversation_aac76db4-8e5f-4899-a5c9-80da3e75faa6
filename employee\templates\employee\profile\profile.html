{% extends 'index.html' %} {% load i18n %} {% block content %}

<section
	class="oh-wrapper oh-main__topbar oh-profile-section__topbar"
	x-data="{searchShow: false}"
>
	<div class="oh-main__titlebar oh-main__titlebar--left">
		<h1 class="oh-main__titlebar-title fw-bold mb-0">
			{% trans "Edit Profile" %}
		</h1>
	</div>
	<div
		class="oh-main__titlebar oh-main__titlebar--left d-flex align-items-center"
	>
		<ul class="oh-general__tabs oh-profile-section__tab">
			<li class="oh-general__tab">
				<a
					href="#"
					class="oh-general__tab-link oh-general__tab-link--active"
					data-action="general-tab"
					data-target="#personal"
					>{% trans "Personal Info" %}</a
				>
			</li>
			<li class="oh-general__tab">
				<a
					href="#"
					class="oh-general__tab-link"
					data-action="general-tab"
					data-target="#bank"
					>{% trans "Bank Info" %}</a
				>
			</li>
		</ul>
	</div>
</section>
<div class="oh-wrapper">
	{% include 'employee/profile/personal_info.html' %}
    {% include 'employee/profile/bank_info.html' %}
</div>

<script>
	$(document).ready(function() {

		  // Active tab script
		function activeEmpprofileTab() {
		  var activeTab = localStorage.getItem("activeEmpprofileTab")

		  if (!["#personal", "#bank"].includes(activeTab)) {
			activeTab = "#personal";
		  }

		  if (!$(activeTab).length && $(`[data-target="#personal"]`).length) {
			$(`[data-target="#personal"]`)[0].click()
		  }else if(activeTab != null){
			$(".oh-general__tab-link--active").removeClass("oh-general__tab-link--active");
			$(`[data-target='${activeTab}']`).addClass("oh-general__tab-link--active");
			$(".oh-general__tab-target").addClass("d-none");
			$(activeTab).removeClass("d-none");
			if($(`[data-target="${activeTab}"]`).length>0){
			  $(`[data-target="${activeTab}"]`)[0].click();
			}
		  }
		}
		activeEmpprofileTab()
		$("[data-action=general-tab]").on("click",function (e) {
		  e.preventDefault();
		  const targetId = $(this).attr('data-target');
		  localStorage.setItem("activeEmpprofileTab",targetId)
		});

	  });

</script>

{% endblock content %}
