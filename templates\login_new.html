
 
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - Horilla Dashboard</title>
    <link rel="stylesheet" href="/static/build/css/style.min.css" />
    <link rel="manifest" href="/static/build/manifest.json" />
    <style>
      /* Help Text Styles */
      .help-text {
        margin-top: 10px;
        text-align: center;
        color: #6c757d; /* Adjust color as needed */
        font-size: 14px; /* Adjust font size as needed */
      }
    </style>
  </head>
  <body>
    <div id="main">
      <div class="oh-alert-container">
        
      </div>
      <main class="oh-auth">
        <div class="oh-auth-card" style="border-radius: 5rem;">
          <h1
            class="oh-onboarding-card__title oh-onboarding-card__title--h2 text-center my-3"
          >
            Sign In
          </h1>
          <p class="text-muted text-center">
            Please login to access the dashboard.
          </p>
          <form method="post" class="oh-form-group">
            <input type="hidden" name="csrfmiddlewaretoken" value="Djz9Nquy7Bxy9aDhNwH7epjJOtZu0RjuL1aYNOga459uIcaDRLkPx6sT134ol6Pm">
            <div class="oh-input-group">
              <label class="oh-label" for="username"
                >Username</label
              >
              <input
                type="text"
                id="username"
                name="username"
                class="oh-input w-100"
     		value="admin"
                placeholder="e.g. <EMAIL>"
              />
            </div>
            <div class="oh-input-group">
              <label class="oh-label" for="password"
                >Password</label
              >
              <div class="oh-password-input-container">
                <input
                  type="password"
                  id="password"
                  name="password"
		  value="admin"
                  class="oh-input oh-input--password w-100"
                  placeholder="Use alphanumeric characters"
                />
                <button
                  type="button"
                  class="oh-btn oh-btn--transparent oh-password-input--toggle"
                >
                  <ion-icon
                    class="oh-passowrd-input__show-icon"
                    title="Show Password"
                    name="eye-outline"
                  ></ion-icon>
                  <ion-icon
                    class="oh-passowrd-input__hide-icon d-none"
                    title="Hide Password"
                    name="eye-off-outline"
                  ></ion-icon>
                </button>
              </div>
            </div>
            <button
              type="submit"
              class="oh-btn oh-onboarding-card__button mt-4 oh-btn--secondary oh-btn--shadow w-100 mb-4"
              role="button"
            >
              <ion-icon class="me-2" name="lock-closed-outline"></ion-icon>
              Secure Sign-in
            </button>
            <!-- Help Text Section -->
            <div class="help-text">
              <p>
                Note: You can use the username 'admin' and password 'admin' to log in.
              </p>
            </div>
            <!-- End Help Text Section -->
            <small class="text-center"
              ><a
                href="/forgot-password"
                class="oh-link oh-link--secondary justify-content-center"
                >Forgot password?</a
              ></small
            >
          </form>
        </div>
        <img
          src="/static/images/ui/auth-logo.png"
          width="150"
          height="41"
          alt="Horilla"
        />
      </main>
    </div>
    <script src="/static/build/js/web.frontend.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script
      type="module"
      src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"
    ></script>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>
    <script>
      $(document).ready(function () {
        if ($(".oh-alert--warning").length > 0) {
          Swal.fire({
            title: 'Access Denied !',
            text: 'Your login credentials are currently blocked. Please contact HR administrators for assistance.',
            icon: 'warning',
            confirmButtonText: 'OK',
          });
        }
      });
    </script>
  </body>
</html>
