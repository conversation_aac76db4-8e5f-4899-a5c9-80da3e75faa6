from django.utils.translation import gettext as _

_("monday"),
_("tuesday"),
_("wednesday"),
_("thursday"),
_("friday"),
_("saturday"),
_("sunday"),
_("after"),
_("weekly"),
_("monthly"),
_("Employee First Name"),
_("Employee Last Name"),
_("Bank Code #1"),
_("Bank Code #2"),
_("RECRUITMENT"),
_("ONBOARDING"),
_("EMPLOYEE"),
_("PAYROLL"),
_("ATTENDANCE"),
_("LEAVE"),
_("ASSET"),
_("Your asset request approved!."),
_("Your asset request rejected!."),
_("You are added to rotating work type"),
_("You are added to rotating shift"),
_("Your work type request has been canceled."),
_("Your work type request has been approved."),
_("Your work type request has been deleted."),
_("Your shift request has been canceled."),
_("Your shift request has been approved."),
_("Your shift request has been deleted."),
_("Your work details has been updated."),
_("You have a new leave request to validate."),
_("New leave type is assigned to you"),
_("Your Leave request has been cancelled"),
_("Your Leave request has been approved"),
_("You are chosen as onboarding stage manager"),
_("You are chosen as onboarding task manager"),
_("You got an OKR!."),
_("You have received feedback!"),
_("You have been assigned as a manager in a feedback!"),
_("You have been assigned as a subordinate in a feedback!"),
_("You have been assigned as a colleague in a feedback!"),
_("You are chosen as one of recruitment manager"),
_("Your attendance for the date "),
_(" is validated"),
_("Select"),
_("January"),
_("February"),
_("March"),
_("April"),
_("May"),
_("June"),
_("July"),
_("August"),
_("September"),
_("October"),
_("November"),
_("December"),
_("One time date"),
_("Is condition based"),
_("Is taxable"),
_("Is fixed"),
_("Value"),
_("If choice"),
_("Is tax"),
_("If amount"),
_("If condition"),
_("Employer rate"),
_("Contract name"),
_("Contract start date"),
_("Contract end date"),
_("Wage type"),
_("Calculate daily leave amount"),
_("Deduction for one leave amount"),
_("Deduct leave from basic pay"),
_("Job role"),
_("Work type"),
_("Pay frequency"),
_("Filing status"),
_("Contract status"),
_("Contract document"),
_("Is tax"),
_("Update compensation"),
_("Is pretax"),
_("DASHBOARD"),
_("SHIFT REQUESTS"),
_("WORK TYPE REQUESTS"),
_("ATTENDANCE"),
_("ASSET"),
_("Single"),
_("Married"),
_("Divorced"),
_("Description"),
_("Rotate every weekend"),
_("Rotate every"),
_("Request description"),
_("Attendance validated"),
_("Is validate request"),
_("Is validate request approved"),
_("Reporting Manager"),
_("Employment Type"),
_("Jan"),
_("Feb"),
_("Mar"),
_("Apr"),
_("May"),
_("Jun"),
_("Jul"),
_("Aug"),
_("Sep"),
_("Oct"),
_("Nov"),
_("Dec"),
_("Additional info"),
_("Schedule date"),
_("Is active"),
_("End date"),
_("Recruitment managers"),
_("Stage managers"),
_("Stage type"),
_("Scheduled from"),
_("Scheduled till"),
_("Start from"),
_("End till"),
_("Employee first name"),
_("Employee last name"),
_("Reporting manager"),
_("Requested date"),
_("Previous shift"),
_("Gte"),
_("Lte"),
_("Previous work type"),
_("Current shift"),
_("Rotating shift"),
_("Next change date"),
_("Next shift"),
_("Current work type"),
_("Next work type"),
_("Start date from"),
_("Start date till"),
_("End date from"),
_("End date till"),
_("Location"),
_("Attendance clock in"),
_("Attendance clock out"),
_("Attendance overtime approve"),
_("Hour account"),
_("Clock out date"),
_("Clock in date"),
_("Shift day"),
_("Attendance date from"),
_("In from"),
_("Out from"),
_("Attendance date till"),
_("Out from"),
_("Out till"),
_("In till"),
_("Leave type"),
_("From date"),
_("To date"),
_("Assigned date"),
_("Based on week"),
_("Based on week day"),
_("Emp obj"),
_("Updated at"),
_("Created at"),
_("Created at date range"),
_("Review cycle"),
_("Asset list"),
_("Query"),
_("Asset category name"),
_("Asset category description"),
_("Asset name"),
_("Asset tracking"),
_("Asset purchase date"),
_("Asset purchase cost"),
_("Asset lot number"),
_("Asset category"),
_("Asset status"),
_("True"),
_("False"),
_("Onboarding Portal S…"),
_("Employee work information"),
_("Rotating work type assign"),
_("Employee shift schedule"),
_("Rotating shift assign"),
_("Onboarding portal"),
_("Start date breakdown"),
_("End date breakdown"),
_("Payment"),
_("dashboard"),
_("pipeline"),
_("recruitment-survey-question-template-view"),
_("candidate-view"),
_("recruitment-view"),
_("stage-view"),
_("view-onboarding-dashboard"),
_("onboarding-view"),
_("candidates-view"),
_("employee-profile"),
_("employee-view"),
_("shift-request-view"),
_("work-type-request-view"),
_("rotating-shift-assign"),
_("rotating-work-type-assign"),
_("view-payroll-dashboard"),
_("view-contract"),
_("view-allowance"),
_("view-deduction"),
_("view-payslip"),
_("filing-status-view"),
_("attendance-view"),
_("request-attendance-view"),
_("attendance-overtime-view"),
_("attendance-activity-view"),
_("late-come-early-out-view"),
_("view-my-attendance"),
_("leave-dashboard"),
_("leave-employee-dashboard"),
_("user-leave"),
_("user-request-view"),
_("type-view"),
_("assign-view"),
_("request-view"),
_("holiday-view"),
_("company-leave-view"),
_("dashboard-view"),
_("objective-list-view"),
_("feedback-view"),
_("period-view"),
_("question-template-view"),
_("asset-category-view"),
_("asset-request-allocation-view"),
_("recruitment"),
_("update-contract"),
_("update-allowance"),
_("update-deduction"),
_("type-update"),
_("type-creation"),
_("asset-batch-view"),
_("create-deduction"),
_("create-allowance"),
_("update-allowance"),
_("update-deduction"),
_("pms"),
_("asset"),
_("leave"),
_("attendance"),
_("payroll"),
_("employee"),
_("onboarding"),
_("recruitment"),
_("settings"),
_("department-view"),
_("job-position-view"),
_("job-role-view"),
_("work-type-view"),
_("rotating-work-type-view"),
_("employee-type-view"),
_("employee-shift-view"),
_("employee-shift-schedule-view"),
_("rotating-shift-view"),
_("attendance-settings-view"),
_("user-group-view"),
_("company-view"),
_("employee-permission-assign"),
_("currency"),
_("leave-allocation-request-view"),
_("employee-view-update"),
_("employee-bulk-update"),
_("not_set"),
_("objective-creation"),
_("feedback-creation"),
_("helpdesk"),
_("faq-category-view"),
_("faq-view"),
_("ticket-view"),
_("ticket-detail"),
_("ticket-type-view"),
_("tag-view"),
_("mail-server-conf"),
_("configuration"),
_("multiple-approval-condition"),
_("skill-zone-view"),
_("view-mail-templates"),
_("view-loan"),
_("view-reimbursement"),
_("department-manager-view"),
_("date-settings"),
_("reporting_manager"),
_("department"),
_("job_position"),
_("job_role"),
_("shift"),
_("work_type"),
_("company"),
_("employee-create-personal-info"),
_("offboarding"),
_("offboarding-pipeline"),
_("pagination-settings-view"),
_("organisation-chart"),
_("document-request-view"),
_("disciplinary-actions"),
_("view-policies"),
_("resignation-requests-view"),
_("action-type"),
_("general-settings"),
_("candidate-update"),
_("create-payslip"),
_("work-records"),
_("edit-profile"),
_("candidate-reject-reasons"),
_("employee-tag-view"),
_("grace-settings-view"),
_("helpdesk-tag-view"),
_("feedback-answer-view"),
_("requested"),
_("approved"),
_("cancelled"),
_("rejected"),
_("true"),
_("false"),
_("candidate-create"),
_("compensatory-leave-settings-view"),
_("view-compensatory-leave"),
_("interview-view"),
_("view-meetings"),
_("view-key-result"),
_("asset-history"),
_("restrict-view"),
_("auto-payslip-settings-view"),
_("bonus-point-setting"),
_("employee-past-leave-restriction"),
_("track-late-come-early-out"),
_("enable-biometric-attendance"),
_("allowed-ips"),
_("self-tracking-feature"),
_("candidate-reject-reasons"),
_("skills-view"),
_("employee-bonus-point"),
_("mail-automations"),
_("check-in-check-out-setting"),
_("user-accessibility"),
_("project"),
_("project-dashboard-view"),
_("project-view"),
_("task-view"),
_("task-all"),
_("view-time-sheet"),
_("backup"),
_("gdrive"),
_("horilla-theme"),
_("color-settings"),
_("report"),
_("recruitment-report"),
_("employee-report"),
_("attendance-report"),
_("leave-report"),
_("payroll-report"),
_("asset-report"),
_("pms-report"),
