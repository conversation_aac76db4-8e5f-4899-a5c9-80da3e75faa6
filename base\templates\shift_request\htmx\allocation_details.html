{% load basefilters %}
{% load i18n %} {% load horillafilters %}
<div class="oh-modal__dialog-header">
    <span
      class="oh-modal__dialog-title"
      id="shiftRequestSingleViewModalLabel"
    >
      {% trans "Details" %}
    </span>
    <button class="oh-modal__close" aria-label="Close">
      <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body oh-modal__dialog-relative" >
{% if request.GET.instances_ids %}

    <div class="oh-modal__dialog oh-modal__dialog--navigation m-0 p-0">
        <button
            hx-get="{% url 'shift-allocation-request-details' previous %}?instances_ids={{allocation_ids}}&dashboard={{dashboard}}"
            hx-target="#objectDetailsModalTarget"
            class="oh-modal__diaglog-nav oh-modal__nav-prev"
            data-action="previous"
            >
            <ion-icon name="chevron-back-outline" class="md hydrated" role="img"
            aria-label="chevron back outline"></ion-icon>
        </button>

        <button
            hx-get="{% url 'shift-allocation-request-details' next %}?instances_ids={{allocation_ids}}&dashboard={{dashboard}}"
            hx-target="#objectDetailsModalTarget"
            class="oh-modal__diaglog-nav oh-modal__nav-next"
            data-action="next"
            >
            <ion-icon name="chevron-forward-outline" class="md hydrated" role="img"
            aria-label="chevron forward outline"></ion-icon>
        </button>
    </div>

{% endif %}
<a class="oh-timeoff-modal__profile-content" style="text-decoration:none;"
    href ="{% url 'employee-view-individual' shift_request.employee_id.id %}">
    <div class="oh-profile mb-2">
        <div class="oh-profile__avatar">
            <img
            src="{{shift_request.employee_id.get_avatar}}"
            class="oh-profile__image me-2"
            alt="Mary Magdalene"
            />
        </div>
        <div class="oh-timeoff-modal__profile-info">
        <span class="oh-timeoff-modal__user fw-bold"
        >{{shift_request.employee_id}}</span
        >
        <span
        class="oh-timeoff-modal__user m-0"
        style="font-size: 18px; color: #4d4a4a"
        >
        {{shift_request.employee_id.employee_work_info.department_id}} /
        {{shift_request.employee_id.employee_work_info.job_position_id}}</span
        >
        </div>
    </div>
</a>

<div class="oh-modal__dialog-header ps-0 pe-0">

    <div class="oh-timeoff-modal__stats-container mb-3">
        <div class="oh-timeoff-modal__stat">
            <span class="oh-timeoff-modal__stat-title">{% trans "Reallocate to" %}</span>
            <span class="oh-timeoff-modal__stat-count">{{shift_request.reallocate_to}}</span>
        </div>
        <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
            <span class="oh-timeoff-modal__stat-title">{% trans "User availability" %}</span>
            <span class="oh-timeoff-modal__stat-count">
                {% if shift_request.reallocate_approved == True %}
                    <div class="d-flex align-items-center">
                        <span class="oh-dot oh-dot--small me-1 oh-dot--color  oh-dot--success "></span><span class=" link-success ">{% trans "Available" %}</span>
                    </div>
                    {% elif shift_request.reallocate_canceled == True %}
                    <div class="d-flex align-items-center">
                        <span class="oh-dot oh-dot--small me-1 oh-dot--color  oh-dot--danger "></span><span class=" link-danger ">{% trans "Not Available" %}</span>
                    </div>
                    {% else %}
                    <div class="d-flex align-items-center">
                        <span class="oh-dot oh-dot--small me-1 oh-dot--color" style="background-color:grey;"></span><span class=" link-secondary ">{% trans "Not Marked" %}</span>
                    </div>
                {% endif %}
            </span>
        </div>
    </div>

    <div class="oh-timeoff-modal__stats-container mb-3">
      <div class="oh-timeoff-modal__stat">
          <span class="oh-timeoff-modal__stat-title">{% trans "Requested shift" %}</span>
          <span class="oh-timeoff-modal__stat-count">{{shift_request.shift_id}}</span>
      </div>
      <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
          <span class="oh-timeoff-modal__stat-title">{% trans "Previous shift" %}</span>
          <span class="oh-timeoff-modal__stat-count">{{shift_request.previous_shift_id}}</span>
      </div>
    </div>
    <div class="oh-timeoff-modal__stats-container mb-3">
      <div class="oh-timeoff-modal__stat">
          <span class="oh-timeoff-modal__stat-title">{% trans "Requested date" %}</span>
          <span class="oh-timeoff-modal__stat-count dateformat_changer">{{shift_request.requested_date}}</span>
      </div>
      <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
          <span class="oh-timeoff-modal__stat-title">{% trans "Requested till" %}</span>
          <span class="oh-timeoff-modal__stat-count dateformat_changer">{{shift_request.requested_till}}</span>
      </div>
    </div>
    <div class="oh-timeoff-modal__stats-container mb-3">
      <div class="oh-timeoff-modal__stat">
          <span class="oh-timeoff-modal__stat-title">{% trans "Description" %}</span>
          <span class="oh-timeoff-modal__stat-count">{{shift_request.description}}</span>
      </div>
    </div>
    <div class="oh-modal__button-container text-center">
       <div class="oh-btn-group">
        {% if dashboard == 'true' %}
            <a
                href="/shift-request-approve/{{shift_request.id}}/"
                onclick="return confirm('{% trans "Do you want to approve this request?" %}')"
                class="oh-btn oh-btn--info w-50"
                title='{% trans "Approve" %}'
                >{% trans "Approve" %}</a>

            <a
                href="/shift-request-cancel/{{shift_request.id}}/"
                onclick="return confirm('{% trans "Do you want to cancel this request?" %}')"
                class="oh-btn oh-btn--danger w-50"
                title='{% trans "Cancel" %}'
                >{% trans "Cancel" %}</a>
        {% else %}
            <!-- start of edit button  -->

            {% if shift_request.approved == False and not shift_request.canceled   %}
                <a hx-get="{% url 'shift-allocation-request-update' shift_request.id %}" hx-target='#shiftRequestModalUpdateBody' data-toggle="oh-modal-toggle" data-target='#shiftRequestModalUpdate' class="oh-btn oh-btn--info w-50" title="{% trans 'Edit' %}"><ion-icon name="create-outline"></ion-icon></a>
            {% else %}
                <button class="oh-btn oh-btn--info w-50" disabled><ion-icon name="create-outline" ></ion-icon></button></td>
            {% endif %}

            <!-- end of edit button  -->

            <!-- start of remove button  -->
            {% if shift_request.approved == False and shift_request.canceled == False %}
                <form action="{% url 'shift-request-delete' shift_request.id  %}" onsubmit="return confirm('{% trans "Are you sure you want to delete this shift request?" %}');" method='post' style="width: 50%">
                    {% csrf_token %}
                    <button type='submit' class="oh-btn oh-btn--secondary w-100" title="{% trans 'Remove' %}"><ion-icon name="trash-outline"></ion-icon></button>
                </form>
            {% elif perms.base.change_shiftrequest or request.user|is_reportingmanager %}
                <form action="{% url 'shift-request-delete' shift_request.id  %}" onsubmit="return confirm('{% trans "Are you sure you want to delete this shift request?" %}');" method='post' style="width: 50%">
                    {% csrf_token %}
                    <button type='submit' class="oh-btn oh-btn--secondary w-100" title="{% trans 'Remove' %}"><ion-icon name="trash-outline"></ion-icon></button>
                </form>
            {% else %}
                <button class="oh-btn oh-btn--secondary w-50" disabled><ion-icon name="trash-outline"></ion-icon></button></td>
            {% endif %}
             <!-- end of remove button  -->
        {% endif %}
       </div>
    </div>
</div>
</div>
