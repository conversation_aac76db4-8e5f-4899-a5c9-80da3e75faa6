{% load i18n %}
<div class="oh-general__tab-target oh-profile-section mb-4 d-none" id="bank">
    <div class="oh-profile-section__card">
        <form action="" method="post">

            {% csrf_token %}
            <div class="row">
                <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label">{% trans "Bank Name" %}</label>
                        {{ bank_form.bank_name }}
                        {{ bank_form.bank_name.errors }}
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label">{% trans "Account Number" %}</label>
                        {{ bank_form.account_number }}
                        {{ bank_form.account_number.errors }}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label">{% trans "Branch" %}</label>
                        {{ bank_form.branch }}
                        {{ bank_form.branch.errors }}
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label">{% trans "Bank Code" %} #1</label>
                        {{ bank_form.any_other_code1 }}
                        {{ bank_form.any_other_code1.errors }}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-sm-12 col-md-12 col-lg-12">
                    <div class="oh-input-group">
                        <label class="oh-label">{% trans "Bank Address" %}</label>
                        {{ bank_form.address }}
                        {{ bank_form.address.errors }}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                    <label class="oh-label" for="country">{% trans "Country" %}</label>
                    <select name="country" id="country" class="oh-select oh-select-2"
                        data-selected="{{ bank_form.country.value|default:'' }}">
                    </select>
                    {{ bank_form.country.errors }}
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label">{% trans "State" %}</label>
                        <select name="state" id="state" class="oh-select oh-select-2"
                            data-selected="{{ bank_form.state.value|default:'' }}">

                        </select>
                        {{ bank_form.state.errors }}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label">{% trans "City" %}</label>
                        {{ bank_form.city }}
                        {{ bank_form.city.errors }}
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label">{% trans "Bank Code" %} #2</label>
                        {{ bank_form.any_other_code2 }}
                        {{ bank_form.any_other_code2.errors }}
                    </div>
                </div>
            </div>
            <hr class="mt-5 mb-3">
            <div class="w-100 d-flex align-items-center justify-content-end">
                <button type="submit" class="oh-btn oh-btn--secondary oh-btn--w-100-resp">
                    {% trans "Save" %}
                </button>
            </div>
        </form>
    </div>
</div>
</div>
