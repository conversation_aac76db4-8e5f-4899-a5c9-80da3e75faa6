{% load i18n %}
<div class="oh-modal__dialog-header pb-0">
    <h5 class="oh-modal__dialog-title" id="objectCreateModalLabel">
        {{form.verbose_name}}
    </h5>
    <button class="oh-modal__close" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body">
    <form id="file-form"
        {% if document_request %}
            hx-post="{% url 'document-request-update' document_request.id %}"
        {% else %}
            {% if form.candidate_id %}
                hx-post="{% url 'candidate-document-request' %}"
            {% else %}
                hx-post="{% url 'document-request-create' %}"
            {% endif %}
        {% endif %}
        hx-target="#objectCreateModalTarget"
        hx-encoding="multipart/form-data" class="oh-profile-section pt-3">
        {{form.errors}}
        <div class="row">
            <div class="col-sm-12 col-md-12 col-lg-12">
                <div class="oh-input-group">
                    <label class="oh-label" for="{{form.title.id_for_label}}">{{form.title.label}}</label>
                    {{form.title}}
                </div>
            </div>
            {% if form.employee_id %}
            <div class="col-sm-12 col-md-12 col-lg-12">
                <div class="oh-input-group">
                    <label class="oh-label" for="{{form.employee_id.id_for_label}}">{{form.employee_id.label}}</label>
                    {{form.employee_id}}
                </div>
            </div>
            {% elif form.candidate_id %}
            <div class="col-sm-12 col-md-12 col-lg-12 {% if form.candidate_id.initial %}d-none{% endif %}">
                <div class="oh-input-group">
                    <label class="oh-label" for="{{form.candidate_id.id_for_label}}">{{form.candidate_id.label}}</label>
                    {{form.candidate_id}}
                </div>
            </div>

            {% endif %}
        </div>
        <div class="row">
            <div class="col-sm-12 col-md-12 col-lg-6">
                <div class="oh-input-group">
                    <label class="oh-label" for="{{form.format.id_for_label}}">{{form.format.label}}</label>
                    {{form.format}}
                </div>
            </div>
            <div class="col-sm-12 col-md-12 col-lg-6">
                <div class="oh-input-group">
                    <label class="oh-label" for="{{form.max_size.id_for_label}}">{{form.max_size.label}}</label>
                    {{form.max_size}}
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 col-md-12 col-lg-12">
                <div class="oh-input-group">
                    <label class="oh-label" for="{{form.description.id_for_label}}">{{form.description.label}}</label>
                    {{form.description}}
                </div>
            </div>
        </div>
        <div class="oh-modal__dialog-footer p-0 mt-3">
            <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
                {% trans "Save" %}
            </button>
        </div>
    </form>
</div>
